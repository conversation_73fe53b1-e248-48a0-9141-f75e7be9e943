<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>ACMS Project</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css"
          crossorigin="anonymous">

    <style>
        body {
            font-family: 'Nunito', sans-serif;
        }

        thead th {
            color: #fea628 !important;
        }

        .thead-dark {
            position: sticky;
            top: 0;
            z-index: 9999;
        }
    </style>
</head>
<body class="antialiased">
<div id="main" class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-3">
                <form action="/roles" method="post">
                    {{ csrf_field() }}
                    <div>
                        @if (\Session::has('success'))
                            <div class="alert alert-success">
                                {!! \Session::get('success') !!}
                            </div>
                        @endif
                        @if (\Session::has('error'))
                            <div class="alert alert-success">
                                {!! \Session::get('error') !!}
                            </div>
                        @endif
                    </div>
                    <div class="card-header">
                        <h2><img src="icon.svg" width="40"> Phân quyền theo roles</h2>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            Save
                        </button>
                        <button type="reset" class="btn">
                            Clear
                        </button>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead class="thead-dark">
                            <tr>
                                <th scope="col"></th>
                                @foreach($roles as $role)
                                    <?php $role_id = $role['id']; ?>
                                    <th scope="col">
                                        {{ $role_id }}.{{ $role['name'] }}
                                        <input
                                               name="selectAll"
                                               id="{{ $role_id }}_"
                                               value="{{ $role_id }}_"
                                               class="" type="checkbox"
                                        />
                                    </th>
                                @endforeach
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($permissions as $key => $group)
                                <tr>
                                    <th></th>
                                </tr>
                                <tr>
                                    <th colspan="5" style="color: #fea628; font-size: 1.2rem">
                                        {{ ucwords($key) }}
                                    </th>
                                </tr>
                                @foreach($group as $permission)
                                    <?php $permission_id = $permission['id']; ?>
                                    <tr>
                                        <th scope="row">{{ $permission_id }}.{{ name_to_desc($permission['name']) }}</th>

                                        @foreach($roles as $role)
                                            <?php $role_id = $role['id']; ?>
                                            <?php
                                            $ids = array_column($role['permissions'], 'permission_id');
                                            $checked = in_array($permission['id'], $ids) ? 'checked="checked"' : '';
                                            ?>
                                            <td>
                                                <div class="form-check">
                                                    <input name="routes[]"
                                                           id="{{ $role_id }}_{{ $permission_id }}"
                                                           value="{{ $role_id }}|{{ $permission_id }}"
                                                           {{ $checked }}
                                                           class="form-check-input" type="checkbox"
                                                    />
                                                </div>
                                            </td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script>
    $(document).ready(function() {
        $(':checkbox[name=selectAll]').click(function () {
            var value = this.value;
            var select = "[id^="+value+"][type=checkbox]";
            $(select).prop('checked', this.checked);
        });
    });
</script>
</body>
</html>
