upstream loadbalancer {
    server  **********:9991 weight=1;
    server  **********:9992 weight=1;
}

server {
    client_max_body_size 200M;
    listen 80;
    index index.php index.html;
    error_log  /var/www/error.log;
    access_log /var/www/access.log;
    root /var/www/public;
    location ~ \.php$ {
        # try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    # location / {
    #     try_files $uri $uri/ /index.php?$query_string;
    #     gzip_static on;
    # }
    location / {
        proxy_pass http://loadbalancer;
    }
}