version: '3'
services:
  app:
    build:
      context: .
      dockerfile: docker/Dockerfilephp80
    image: app
    restart: unless-stopped
    tty: true
    environment:
      CONTAINER_ROLE: app
      SERVICE_NAME: app
      SERVICE_TAGS: dev
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - app-network
  webserver:
    image: nginx:alpine
    restart: unless-stopped
    tty: true
    ports:
      - ${APP_PORT}:80
    volumes:
      - ./:/var/www
      - ./docker/nginx/app.d/:/etc/nginx/conf.d/
    networks:
      - app-network
  queue:
    image: app
    depends_on:
      - app
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    environment:
      CONTAINER_ROLE: queue
#Docker Networks
networks:
  app-network:
    driver: bridge
#Volumes
volumes:
  dbdata:
    driver: local
  mongodb:
    driver: local
  mongoconfig:
    driver: local
