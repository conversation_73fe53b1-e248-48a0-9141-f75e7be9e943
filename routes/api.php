<?php

use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\AllocationController;
use App\Http\Controllers\ChargeRateController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DailyReportController;
use App\Http\Controllers\DeliverableController;
use App\Http\Controllers\IdleWorkforceController;
use App\Http\Controllers\MasterDataController;
use App\Http\Controllers\MilestoneController;
use App\Http\Controllers\PcvReportController;
use App\Http\Controllers\PmReportController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\RequirementChangeController;
use App\Http\Controllers\ResourceController;
use App\Http\Controllers\ResourceRentalCostController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\StageController;
use App\Http\Controllers\TechnologyController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserUnpaidLeaveController;
use App\Http\Controllers\StaffCoefficientController;
use App\Http\Controllers\RuleController;
use App\Http\Controllers\PenaltyController;
use App\Http\Controllers\RulePenaltyController;
use App\Http\Controllers\ViolationReportController;
use App\Http\Controllers\WeeklyReportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['middleware' => ['jwt.verify']], function () {
    Route::prefix('projects')
        ->name('projects.')
        ->group(function () {
            Route::group(['middleware' => ['auth']], function () {
                Route::get('/wf-requests', [ProjectController::class, 'getRequestProject'])->name('requesting');
                Route::resource('', ProjectController::class)->parameters(['' => 'projects']);
                Route::post('/{id}/members', [ProjectController::class, 'addMember'])->name('members.store');
                Route::delete('/{project_id}/members/{id}/{member_id}', [ProjectController::class, 'removeMember'])->name('members.destroy');
                Route::resource('{project_id}/requirement-change', RequirementChangeController::class);
                Route::post('{project_id}/requirement-change/import', [RequirementChangeController::class, 'import'])->name('requirement-change.import');
                Route::post('{project_id}/requirement-change/{requirement_change_id}', [RequirementChangeController::class, 'update'])->name('requirement-change.update');
                Route::delete('{project_id}/requirement-change', [RequirementChangeController::class, 'destroy'])->name('requirement-change.destroy');
                Route::resource('{id}/pcv-reports', PcvReportController::class);
                Route::resource('{project_id}/pm-report', PmReportController::class);
                Route::get('/{id}/ot', [ProjectController::class, 'getOT'])->name('ot.index');
                Route::get('/{id}/statistics-employee-leave', [ProjectController::class, 'statisticEmployeeLeave'])->name('ot.total');
                Route::get('/{id}/employee-leave', [ProjectController::class, 'employeeLeave'])->name('ot.total');
                Route::get('/{id}/export-ot', [ProjectController::class, 'exportOT'])->name('ot.index');
                Route::get('/{id}/ot-in-week', [ProjectController::class, 'getAllOT'])->name('ot.in.week');
                Route::get('/{id}/total-ot', [ProjectController::class, 'getTotalOverTimeProject'])->name('ot.total');

                Route::group(['prefix' => '{project_id}/stages', 'as' => 'stages.'], function () {
                    Route::resource('', StageController::class)->parameters(['' => 'stages']);
                    Route::resource('{id}/allocations', AllocationController::class);
                    Route::resource('{id}/milestones', MilestoneController::class);
                    Route::resource('{id}/deliverables', DeliverableController::class);
                    Route::get('{id}/resource-allocation', [StageController::class, 'getResourceAllocation'])->name('resource-allocation');
                    Route::post('{id}/clone', [StageController::class, 'clone'])->name('clone');
                });

                Route::get('/{id}/deliverables', [ProjectController::class, 'getListDeliverable']);
                Route::get('/{id}/milestones', [ProjectController::class, 'getListMilestone']);
                Route::get('{project_id}/log-activities/', [ActivityLogController::class, 'index'])->name('log-activities');
                Route::get('/{id}/overview', [ProjectController::class, 'overview'])->name('overview');
                Route::get('{id}/schedule', [StageController::class, 'getSchedule'])->name('overview.schedule');
                Route::get('/{project_id}/budget', [ProjectController::class, 'getProjectBudget'])->name('budget-by-year.detail');
                Route::put('/{project_id}/budget', [ProjectController::class, 'updateProjectBudget'])->name('budget-by-year.update');
                Route::get('/{id}/data-ee-chart', [ProjectController::class, 'getDataEEChart'])->name('ee-chart');

                Route::get('{id}/daily-reports', [DailyReportController::class, 'getList'])->name('daily-reports');
                Route::get('{id}/daily-reports-enough', [DailyReportController::class, 'getListEnough'])->name('daily-reports-enough');
                Route::get('{id}/daily-reports-not-enough', [DailyReportController::class, 'getListNotEnough'])->name('daily-reports-not-enough');
                Route::get('{id}/export-daily-reports', [DailyReportController::class, 'exportDailyReport'])->name('daily-reports.export');
                Route::get('{id}/export-allocation', [DailyReportController::class, 'exportAllocation'])->name('daily-reports.export');
                Route::put('{id}/change-status-daily-reports', [DailyReportController::class, 'changeStatus'])->name('daily-reports.change-status');
                Route::resource('{id}/resource-rental-costs', ResourceRentalCostController::class);
                Route::get('{id}/export-allocation-statistics', [ProjectController::class, 'exportAllocationStatistics'])->name('daily-reports.export');

                Route::group(['prefix' => '{id}/performance', 'as' => 'performance.'], function () {
                    Route::get('', [ProjectController::class, 'statisticPerformance'])->name('performance');
                    Route::post('import', [ProjectController::class, 'importPerformance'])->name('import');
                });
            });

            Route::post('/wf', [ProjectController::class, 'storeFromWF']);
            Route::post('/check-input-wf', [ProjectController::class, 'checkInputFromWF']);
            Route::get('/{id}/members', [ProjectController::class, 'getMember'])->name('members.index');
            Route::get('{project_id}/pm-reported', [PmReportController::class, 'getDateReported']);
            Route::post('{project_id}/request-open-project-dl', [ProjectController::class, 'requestOpenProjectDl'])->name('request-project-dl');
        });

    Route::resource('/technologies', TechnologyController::class);
    Route::get('/master-data', [MasterDataController::class, 'getMasterData']);
    Route::resource('customers', CustomerController::class);
    Route::get('list-customer', [CustomerController::class, 'getCustomers']);
    Route::get('users', [UserController::class, 'getUsers']);
    Route::get('levels', [UserController::class, 'getLevel']);
    Route::get('positions', [UserController::class, 'getPositions']);
    Route::get('divisions', [UserController::class, 'getDivisions']);
    Route::get('teams', [UserController::class, 'getTeams']);
    Route::put('positions/{id}/update-status', [UserController::class, 'updatePositionStatus']);
    Route::put('divisions/{id}/update-status', [UserController::class, 'updateDivisionStatus']);
    Route::resource('roles', RoleController::class);
    Route::get('skills', [UserController::class, 'getSkills']);
    Route::get('level', [UserController::class, 'getLevel']);
    Route::get('requirement-change/template/download', [RequirementChangeController::class, 'downloadTemplateImport']);
    Route::get('user-permission', [RoleController::class, 'getUserPermission']);

    Route::group(['middleware' => ['auth']], function () {
        Route::post('/customers-sync', [CustomerController::class, 'syncCustomers'])->name('customers.sync');

        Route::group(['as' => 'dashboard.'], function () {
            Route::group(['as' => 'resources.'], function () {
                Route::get('resources', [ResourceController::class, 'index'])->name('index');
                Route::get('idle-workforce', [IdleWorkforceController::class, 'index'])->name('index');
                Route::get('man-month-position', [ResourceController::class, 'getManMonthPosition'])->name('man.month.position');
                Route::get('man-month-skill', [ResourceController::class, 'getManMonthSkill'])->name('man.month.skill');
                Route::get('busy-rate-division', [ResourceController::class, 'getBusyRateDivision'])->name('busy.rate.division');
                Route::get('busy-rate-month-v2', [ResourceController::class, 'getBusyRateMonthV2'])->name('busy.rate.month');
                Route::get('busy-rate-v2', [ResourceController::class, 'getBusyRateV2'])->name('busy.rate');
                Route::get('man-month-project-type', [ResourceController::class, 'getManMonthProjectType'])->name('man.month.project.type');
                Route::get('export-resource-allocation', [ResourceController::class, 'exportResourceAllocation'])->name('export-resource-allocation');
                Route::get('division-effort', [ResourceController::class, 'getDivisionEffort'])->name('division.effort');
                Route::get('team-effort', [ResourceController::class, 'getTeamEffort'])->name('division.effort');
                Route::get('project-effort', [ResourceController::class, 'getProjectEffort'])->name('project.effort');
                Route::get('resource-effort', [ResourceController::class, 'getResourceEffort'])->name('resource.effort');
                Route::get('division-overview', [ResourceController::class, 'getDivisionOverview'])->name('division.overview');
                Route::get('calendar-effort', [ResourceController::class, 'getCalendarEffort'])->name('calendar.effort');
                Route::get('export-log-work', [ResourceController::class, 'exportLogWork'])->name('export.log-work');
                Route::get('available-employees', [IdleWorkforceController::class, 'getAvailableEmployee'])->name('man.month.position');
                Route::get('idle-workforce-division-stats', [IdleWorkforceController::class, 'getDivisionStatistic'])->name('man.month.position');
                Route::get('idle-workforce-month-stats', [IdleWorkforceController::class, 'getMonthStatistic'])->name('man.month.position');
                Route::get('idle-workforce-position-skill-stats', [IdleWorkforceController::class, 'getPositionAndSkillStatistic'])->name('man.month.position');
            });
        });

        Route::get('pm-reports', [PmReportController::class, 'getPmReports'])->name('report.weekly');
        Route::get('project-budget-statistics', [ProjectController::class, 'getProjectBudgetStatistics'])->name('project.budget.statistics');
        Route::get('project-statistics', [ProjectController::class, 'getProjectStatistics'])->name('project.statistics');
        Route::get('division-charge-rate-statistics', [ChargeRateController::class, 'getDivisionChargeRateStatistics'])->name('division-charge-rate-statistics');
        Route::get('division-man-month-list', [ChargeRateController::class, 'getDivisionManMonthList'])->name('division-charge-rate-statistics');
        Route::get('export-division-charge-rate', [ChargeRateController::class, 'export'])->name('division-charge-rate-statistics');
        Route::get('division-charge-rate-statistics-chart', [ChargeRateController::class, 'getDivisionChargeRateStatisticsChart'])->name('division-charge-rate-statistics-chart');
        Route::post('project-budget', [ProjectController::class, 'createProjectBudget'])->name('budget-by-year.create');

        Route::group(['prefix' => 'resource-rental-costs', 'as' => 'resource-rental-costs.'], function () {
            Route::get('', [ChargeRateController::class, 'getResourceRentalCosts'])->name('index');
            Route::post('', [ChargeRateController::class, 'updateResourceRentalCosts'])->name('update');
        });
    });

    Route::get('list-project', [ProjectController::class, 'getListProject']);
    Route::get('projects/{id}/list-member', [ProjectController::class, 'getListMemberInProject']);
    Route::resource('user-paid-leave', UserUnpaidLeaveController::class);
    Route::resource('staff-coefficients', StaffCoefficientController::class);
    Route::get('list-holiday', [ProjectController::class, 'getListHoliday']);
    Route::get('/user-projects', [ProjectController::class, 'getProjectsOfUser']);
    Route::get('contract-types', [ResourceController::class, 'getContractTypes']);
    Route::get('/project-export', [ProjectController::class, 'exportProject'])->name('ot.export');

    Route::get('/daily-report-statistics', [ProjectController::class, 'getDailyReportStatistic']);
    Route::get('/daily-report-detail', [DailyReportController::class, 'getDailyReportStatisticDetail']);

    Route::post('tasks', [ProjectController::class, 'updateTasks']);
    Route::get('performance-member', [ProjectController::class, 'getPerformanceMember']);

    Route::resource('rules', RuleController::class);
    Route::resource('penalties', PenaltyController::class);
    Route::resource('rule-penalties', RulePenaltyController::class);
    Route::get('violation-reports/log-activities', [ViolationReportController::class, 'getLogActivities']);
    Route::resource('violation-reports', ViolationReportController::class);
    Route::post('violation-reports/{id}', [ViolationReportController::class, 'update']);
    Route::resource('weekly-reports', WeeklyReportController::class);
    Route::get('export-weekly-report', [WeeklyReportController::class, 'export'])->name('export-weekly-report');
});

Route::get('/sync-google-sheets-workspace', [ProjectController::class, 'syncGoogleSheetsWorkspace']);
Route::get('/sync-google-sheets-log-work', [ResourceController::class, 'syncGoogleSheetsLogWork']);
Route::get('/certificates', [UserController::class, 'getListCertificateCategory']);