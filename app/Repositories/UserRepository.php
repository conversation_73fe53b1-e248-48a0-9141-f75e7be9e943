<?php

namespace App\Repositories;

use App\Models\User;

class UserRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    public function model()
    {
        return User::class;
    }

    public function getUsersWithFilter($filters = [], $fields = ['*'])
    {
        return $this->model
            ->select($fields)
            ->when(@$filters['user_id'], function ($query) use ($filters) {
                $query->whereIn('user_id', (array)$filters['user_id']);
            })
            ->when(@$filters['status'], function ($query) use ($filters) {
                $query->whereIn('status', (array)$filters['status']);
            })
            ->when(@$filters['division_id'], function ($query) use ($filters) {
                $query->whereIn('division.id', (array)$filters['division_id']);
            })
            ->when(@$filters['position_id'], function ($query) use ($filters) {
                $query->whereIn('position.id', (array)$filters['position_id']);
            })
            ->when(@$filters['level_id'], function ($query) use ($filters) {
                $query->whereIn('level.id', (array)$filters['level_id']);
            })
            ->when(!empty($param['skill']), function ($query) use ($filters) {
                return $query->whereIn('skills.skill_id', (array)$filters['skill_id']);
            })
            ->when(@$filters['keyword'], function ($query) use ($filters) {
                return $query->where(function ($query) use ($filters) {
                    return $query
                        ->where('name', 'like', '%' . $filters['keyword'] . '%')
                        ->orWhere('code', 'like', '%' . $filters['keyword'] . '%')
                        ->orWhere('email', 'like', '%' . $filters['keyword'] . '%');
                });
            })
            ->orderBy('code', 'desc')
            ->get();
    }

    public function getUsers($args)
    {
        if (isset($args['fields'])) {
            $query = $this->model->select($args['fields']);
        } else {
            $query = $this->model->select('*');
        }

        if (isset($args['is_active']) && ($args['is_active'] == 'true' || $args['is_active'] === true || $args['is_active'] == 1)) {
            $query->where('status', EnumUserInfor::ACTIVE);
        }

        if (isset($args['start_date']) && isset($args['end_date'])) {
            $query->whereNull('checkout_date')
                ->orWhere('checkout_date', '>=',  $args['start_date']);
        }


        if ($args['ignore_position'] ?? null) {
            $query->whereNotIn('position.name', EnumUserInfor::IGNORE_POSITIONS);
        }

        if (isset($args['get_dl']) && $args['get_dl'] == 0) {
            $query->whereNotIn('position.name', EnumUserInfor::IGNORE_POSITIONS);
        }

        if (isset($args['position'])) {
            $query->whereIn('position.name', $args['position']);
        }

        if (isset($args['position_ids'])) {
            $positionIds = array_map(function ($id) {
                return (int)$id;
            }, $args['position_ids']);
            $query->whereIn('position.id', $positionIds);
        }

        if (isset($args['division'])) {
            $query->whereIn('division.name', $args['division']);
        }

        if (isset($args['division_ids'])) {
            $divisionIds = array_map(function ($id) {
                return (int)$id;
            }, $args['division_ids']);
            $query->whereIn('division.id', $divisionIds);
        }

        if (isset($args['team'])) {
            $query->whereIn('team.name', $args['team']);
        }

        if (isset($args['team_ids'])) {
            $teamIds = array_map(function ($id) {
                return (int)$id;
            }, $args['team_ids']);
            $query->whereIn('team.id', $teamIds);
        }

        if (isset($args['skill'])) {
            $query->whereIn('skills.skill', $args['skill']);
        }

        if (isset($args['skill_ids'])) {
            $skillIds = array_map(function ($id) {
                return (int)$id;
            }, $args['skill_ids']);
            $query->whereIn('skills.skill_id', $skillIds);
        }

        if (isset($args['level'])) {
            $query->whereIn('level.name', $args['level']);
        }

        if (isset($args['level_ids'])) {
            $levelIds = array_map(function ($id) {
                return (int)$id;
            }, $args['level_ids']);
            $query->whereIn('level.id', $levelIds);
        }

        if (!empty($args['name'])) {
            $query->where(function ($query) use ($args) {
                $query->orWhere('name', 'like', '%' . $args['name'] . '%')
                    ->orWhere('code', 'like', '%' . $args['name'] . '%')
                    ->orWhere('email', 'like', '%' . $args['name'] . '%')
                    ->orWhere('completed_projects.industry', 'like', '%' . $args['name'] . '%')
                    ->orWhere('completed_projects.content', 'like', '%' . $args['name'] . '%');
            });
        };

        if (isset($args['user_ids'])) {
            $ids = array_map(function ($id) {
                return (int)$id;
            }, $args['user_ids']);
            $query->whereIn('user_id', $ids);
        }

        if (isset($args['contract_types'])) {
            $contractTypes = array_map(function ($id) {
                return (int)$id;
            }, (array)$args['contract_types']);

            $query->where(function ($q) use ($contractTypes) {
                $q->whereIn('contract.contract_category_id', $contractTypes)
                    ->orWhereNull('contract');
            });
        }

        if (isset($args['pagination']) && ($args['pagination'] == 'true' || $args['pagination'] === true)) {
            $limit = isset($args['limit']) ? (int)$args['limit'] : 50;
            return $query->orderBy('code')->paginate($limit);
        }
        return $query->orderBy('code')->get();
    }
}
