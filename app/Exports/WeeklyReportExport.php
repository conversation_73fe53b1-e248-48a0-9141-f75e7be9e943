<?php

namespace App\Exports;

use App\Enums\EWeeklyReport;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class WeeklyReportExport implements WithMultipleSheets, ShouldAutoSize
{
    protected $weeklyReports;

    public function __construct($weeklyReports)
    {
        $this->weeklyReports = $weeklyReports;
    }

    public function sheets(): array
    {
        $sheets = [
            new WeeklyReportExecuteProjectExport(
                $this->filterColumns($this->weeklyReports, EWeeklyReport::COLUMNS_EXPORT_PROJECTS),
                EWeeklyReport::COLUMNS_EXPORT_PROJECTS
            )
        ];
        $divisionReports = collect($this->weeklyReports)->groupBy('division_name')->toArray();
        foreach ($divisionReports as $divisionName => $divisionReport) {
            $sheets[] = new WeeklyReportByDivisionExport(
                $this->filterColumns($divisionReport, EWeeklyReport::COLUMNS_EXPORT),
                EWeeklyReport::COLUMNS_EXPORT,
                $divisionName
            );
        }

        return $sheets;
    }

    private function filterColumns(array $data, array $columns)
    {
        // Lọc các key và sắp xếp theo thứ tự của $columns
        return array_map(fn($item) => array_merge($columns, array_intersect_key($item, $columns)), $data);
    }
}
