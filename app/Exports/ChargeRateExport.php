<?php

namespace App\Exports;

use App\Invoice;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ChargeRateExport implements FromView
{
    protected $chargeRates;
    protected $months;
    public function __construct($chargeRates,$months)
    {
        $this->chargeRates = $chargeRates;
        $this->months = $months;
    }
    public function view(): View
    {
        return view('exports.charge-rate', [
            'chargeRates' => $this->chargeRates,
            'months' => $this->months
        ]);
    }
}
