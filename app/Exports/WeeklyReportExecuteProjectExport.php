<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WeeklyReportExecuteProjectExport implements FromArray, WithHeadings, WithStyles, WithTitle, WithColumnWidths
{
    protected $weeklyReports;
    protected $columns;

    public function __construct(array $weeklyReports, array $columns)
    {
        $this->weeklyReports = $weeklyReports;
        $this->columns = $columns;
    }

    public function array(): array
    {
        return $this->weeklyReports;
    }

    public function headings(): array
    {
        return $this->columns;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1    => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            "C" => [
                'alignment' => [
                    'wrapText' => true
                ],
            ],
        ];
    }

    public function title(): string
    {
        return 'Execute Project';
    }

    public function columnWidths(): array
    {
        $sizeSmall = 15;
        $sizeMedium = 25;
        $sizeLarge = 35;

        return [
            'A' => $sizeSmall,
            'B' => $sizeLarge,
            'C' => $sizeMedium,
            'D' => $sizeMedium,
            'E' => $sizeSmall,
            'F' => $sizeSmall,
            'G' => $sizeSmall,
            'H' => $sizeSmall,
            'I' => $sizeLarge,
            'J' => $sizeMedium,
            'K' => $sizeSmall,
        ];
    }
}
