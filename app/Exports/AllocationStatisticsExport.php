<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AllocationStatisticsExport implements FromView
{
    protected $allocates;
    protected $months;
    public function __construct($allocates, $months)
    {
        $this->allocates = $allocates;
        $this->months = $months;
    }
    public function view(): View
    {
        return view('exports.allocates-statistic', [
            'allocates' => $this->allocates,
            'months' => $this->months
        ]);
    }
}
