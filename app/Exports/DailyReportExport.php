<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DailyReportExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    protected $dailyReports;
    protected $columns;

    public function __construct(array $dailyReports, array $columns)
    {
        $this->dailyReports = $dailyReports;
        $this->columns = $columns;
    }

    public function array(): array
    {
        return $this->dailyReports;
    }

    public function headings(): array
    {
        return $this->columns;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'wrap' => true
                ],
            ],
            "I" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_JUSTIFY,
                ],
            ],
            "I1" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_GENERAL,
                ],
            ],
            "J" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_JUSTIFY,
                ],
            ],
            "J1" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_GENERAL,
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        $sizeSmall = 20;
        $sizeMedium = 40;
        $sizeLarge = 60;

        return [
            'A' => $sizeSmall,
            'B' => $sizeSmall,
            'C' => $sizeSmall,
            'D' => $sizeMedium,
            'E' => $sizeSmall,
            'F' => $sizeSmall,
            'G' => $sizeMedium,
            'H' => $sizeSmall,
            'I' => $sizeLarge,
            'J' => $sizeLarge,
        ];
    }
}
