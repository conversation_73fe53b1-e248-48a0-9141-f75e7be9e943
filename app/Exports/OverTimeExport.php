<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OverTimeExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    protected $overTimes;
    protected $columns;

    public function __construct(array $overTimes, array $columns)
    {
        $this->overTimes = $overTimes;
        $this->columns = $columns;
    }

    public function array(): array
    {
        return $this->overTimes;
    }

    public function headings(): array
    {
        return $this->columns;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'wrap' => true
                ],
            ],
            "F" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_JUSTIFY,
                ],
            ],
            "F1" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        $sizeSmall = 20;
        $sizeMedium = 40;
        $sizeLarge = 60;

        return [
            'A' => $sizeMedium,
            'B' => $sizeSmall,
            'C' => $sizeSmall,
            'D' => $sizeSmall,
            'E' => $sizeSmall,
            'F' => $sizeLarge,
        ];
    }
}
