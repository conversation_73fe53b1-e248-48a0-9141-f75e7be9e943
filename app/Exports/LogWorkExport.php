<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class LogWorkExport implements FromView
{
    protected $users;
    protected $days;
    public function __construct($users, $days)
    {
        $this->users = $users;
        $this->days = $days;
    }

    public function view(): View
    {
        return view('exports.log-work', [
            'users' => $this->users,
            'days' => $this->days
        ]);
    }
}
