<?php

namespace App\Jobs;

use App\Traits\ApiResponse;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendTeamsLogWorkNoticeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ApiResponse;

    protected $content;
    protected $url;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $content, string $url)
    {
        $this->content = $content;
        $this->url = $url;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->sendTeamsLogWorkNotice($this->content, $this->url);
    }
}
