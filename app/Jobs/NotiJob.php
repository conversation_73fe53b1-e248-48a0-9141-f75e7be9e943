<?php

namespace App\Jobs;

use App\Enums\ENoti;
use App\Traits\ApiResponse;
use App\Traits\HttpClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NotiJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use HttpClient;
    use ApiResponse;

    protected $notificationDomain;

    protected $project_id;

    protected $user_id;

    protected $message;

    protected $sender_avatar;

    protected $type;

    protected $detail;

    /**
     * NotiJob constructor.
     *
     * @param $project_id
     * @param $sender_avatar
     * @param $user_id
     * @param $message
     * @param $type
     * @param $detail
     */
    public function __construct(
        $project_id,
        $user_id,
        $message,
        $sender_avatar,
        $type,
        $detail
    ) {
        $this->notificationDomain = config('services.notification.domain');

        $this->project_id = $project_id;
        $this->sender_avatar = $sender_avatar;
        $this->user_id = $user_id;
        $this->message = $message;
        $this->type = $type;
        $this->detail = $detail;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->push(
                $this->project_id,
                $this->user_id,
                $this->message,
                $this->sender_avatar,
                $this->type,
                $this->detail
            );
        } catch (\Exception $e) {
            $this->getError($e);
        }
    }

    public function push($project_id, $user_id, $message, $sender_avatar, $type, $detail)
    {
        $pushApi = config('services.notification.push');

        $data = [
            'system_id' => ENoti::SYSTEM_PROJECT,
            'project_id' => $project_id,
            'user_id' => [$user_id],
            'message' => $message,
            'sender_avatar' => $sender_avatar,
            'type' => $type,
            'detail' => $detail,
        ];

        return $this->httpPost("{$this->notificationDomain}/$pushApi", $data);
    }
}
