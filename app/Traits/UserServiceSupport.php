<?php

namespace App\Traits;

trait UserServiceSupport
{
    use HttpClient;

    public function __getUsers($args = [])
    {
        /**
         * parameter too long, so the POST method is required
         */
        $urlGetUsers = env('LINK_USER_SERVICE').'users';
        $response = $this->httpPost($urlGetUsers, $args);
        $users = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $users;
    }

    public function __getUsersFreetext($args)
    {
        $urlGetUsers = env('LINK_USER_SERVICE').'users';
        $response = $this->httpGet($urlGetUsers, $args);
        $users = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $users;
    }

    public function __getLevels($args = null)
    {
        $urlGetPosition = env('LINK_USER_SERVICE').'levels';
        $response = $this->httpGet($urlGetPosition, $args);
        $positions = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $positions;
    }

    public function __getPositions($args = null)
    {
        $urlGetPosition = env('LINK_USER_SERVICE').'position';
        $response = $this->httpGet($urlGetPosition, $args);
        $positions = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $positions;
    }

    public function __getOT($args, $accessToken)
    {
        $urlGetOT = env('LINK_USER_SERVICE').'export/list-OT';
        $response = $this->httpGetWithToken($accessToken, $urlGetOT, $args);
        $positions = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $positions;
    }

    public function __getUserGroup($args = [])
    {
        $urlGetUsers = env('LINK_USER_SERVICE').'group-user';
        $response = $this->httpGet($urlGetUsers, $args);
        $users = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $users;
    }

    public function __getSkill($args = [])
    {
        $urlGetUsers = env('LINK_USER_SERVICE').'skills';
        $response = $this->httpGet($urlGetUsers, $args);
        $users = isset(json_decode($response)->skills)
            ? json_decode($response)->skills
            : [];

        return $users;
    }

    public function __getLevel($args = [])
    {
        $urlLevel = env('LINK_USER_SERVICE').'level';
        $response = $this->httpGet($urlLevel, $args);
        $users = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $users;
    }

    public function __getListCertificateCategory()
    {
        $url = env('LINK_USER_SERVICE').'list-certificate-category';
        $response = $this->httpGet($url);
        $certificateCategories = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $certificateCategories;
    }
}
