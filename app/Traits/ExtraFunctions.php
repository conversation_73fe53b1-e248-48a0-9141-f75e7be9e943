<?php

namespace App\Traits;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use DatePeriod;
use DateTime;
use App\Enums\EBase;

trait ExtraFunctions
{
    use HrServiceSupport;

    public function calManMonthSingleMonth($startDate, $endDate, $allocate, $month, $holiday)
    {
        $from = Carbon::createFromFormat('Y-m-d', $startDate);
        $to = Carbon::createFromFormat('Y-m-d', $endDate);
        $tmpFrom = clone $from;
        $thisMonth = Carbon::createFromFormat('Y-m-d', $month);
        $daysInMonth = $this->numberOfWorkingDays(
            $thisMonth->startOfMonth()->format('Y-m-d'),
            $thisMonth->endOfMonth()->format('Y-m-d'),
            $holiday
        );
        $startOfMonth = clone $from;
        $tmpThisMonth = clone $thisMonth;
        $tmpTo = clone $to;
        $monthsInThisMoment = $startOfMonth->startOfMonth()->diffInMonths($tmpThisMonth->startOfMonth());
        $monthsInWholeProject = $startOfMonth->startOfMonth()->diffInMonths($tmpTo->startOfMonth());
        if ($from->format('F Y') == $thisMonth->format('F Y')) {
            $monthsInThisMoment = 0;
            $monthsInWholeProject = 0;
        }
        if ($monthsInThisMoment == 0) {
            $dayEndOfMonth = $tmpFrom->endOfMonth();
            if ($dayEndOfMonth < $to) $to = $dayEndOfMonth;
            $actualWorkingDay = $this->numberOfWorkingDays($from->format('Y-m-d'), $to->format('Y-m-d'), $holiday);
        } elseif ($monthsInThisMoment < $monthsInWholeProject) {
            $actualWorkingDay = $daysInMonth;
        } elseif ($monthsInThisMoment == $monthsInWholeProject) {
            $from = $thisMonth->startOfMonth();
            $actualWorkingDay = $this->numberOfWorkingDays($from->format('Y-m-d'), $to->format('Y-m-d'), $holiday);
        }

        $workingDays = $this->numberOfWorkingDays(
            $thisMonth->firstOfMonth()->format('Y-m-d'),
            $thisMonth->endOfMonth()->format('Y-m-d'),
            $holiday
        );
        $allocatedOfMonth = $allocate * $actualWorkingDay / $workingDays ?? 30;


        return $allocatedOfMonth;
    }

    public function calManMonthSingleMonthWithCoefficient($startDate, $endDate, $allocate, $coefficient, $month, $holiday)
    {
        if ($startDate > $endDate) {
            return 0;
        }
        return $this->calManMonthSingleMonth($startDate, $endDate, $allocate, $month, $holiday) * $coefficient / 100;
    }

    public function calManMonthSingleMonthWithOnboardDate($startDate, $endDate, $allocate, $month, $onboardDate, $holiday, $daysLeave = null)
    {
        $from = Carbon::createFromFormat('Y-m-d', $startDate);
        $to = Carbon::createFromFormat('Y-m-d', $endDate);
        $tmpFrom = clone $from;
        $thisMonth = Carbon::createFromFormat('Y-m-d', $month);
        $daysInMonth = $this->numberOfWorkingDays(
            $thisMonth->startOfMonth()->format('Y-m-d'),
            $thisMonth->endOfMonth()->format('Y-m-d'),
            $holiday
        );
        $startOfMonth = clone $from;
        $tmpThisMonth = clone $thisMonth;
        $tmpTo = clone $to;
        $monthsInThisMoment = $startOfMonth->startOfMonth()->diffInMonths($tmpThisMonth->startOfMonth());
        $monthsInWholeProject = $startOfMonth->startOfMonth()->diffInMonths($tmpTo->startOfMonth());
        if ($from->format('F Y') == $thisMonth->format('F Y')) {
            $monthsInThisMoment = 0;
            $monthsInWholeProject = 0;
        }
        if ($monthsInThisMoment == 0) {
            $dayEndOfMonth = $tmpFrom->endOfMonth();
            if ($dayEndOfMonth < $to) $to = $dayEndOfMonth;
            $actualWorkingDay = $this->numberOfWorkingDays($from->format('Y-m-d'), $to->format('Y-m-d'), $holiday);
        } elseif ($monthsInThisMoment < $monthsInWholeProject) {
            $actualWorkingDay = $daysInMonth;
        } elseif ($monthsInThisMoment == $monthsInWholeProject) {
            $from = $thisMonth->startOfMonth();
            $actualWorkingDay = $this->numberOfWorkingDays($from->format('Y-m-d'), $to->format('Y-m-d'), $holiday);
        }

        $workingDays = $this->numberOfWorkingDays(
            $thisMonth->firstOfMonth()->format('Y-m-d'),
            $thisMonth->endOfMonth()->format('Y-m-d'),
            $holiday
        );

        if ($onboardDate && $from->startOfMonth()->eq(Carbon::parse($onboardDate)->startOfMonth()) === true) {
            $workingDays = $this->numberOfWorkingDays($onboardDate, $thisMonth->endOfMonth()->format('Y-m-d'), $holiday);
        }
        if (isset($daysLeave)) $workingDays = $workingDays - $daysLeave;
        $allocatedOfMonth = $allocate * $actualWorkingDay / $workingDays;

        return $allocatedOfMonth;
    }

    public function numberOfWorkingDays($from, $to, $holiday = [])
    {
        $workingDays = [1, 2, 3, 4, 5]; # date format = N (1 = Monday, ...)
        $from = new DateTime($from);
        $to = new DateTime($to);
        $to->modify('+1 day');
        $interval = new DateInterval('P1D');
        $periods = new DatePeriod($from, $interval, $to);

        $days = 0;
        foreach ($periods as $period) {
            if (!in_array($period->format('N'), $workingDays)) continue;
            if (in_array($period->format('Y-m-d'), $holiday)) continue;
            // if (in_array($period->format('*-m-d'), $holidayDays)) continue;
            $days++;
        }

        return $days;
    }

    // $from: Y-m-d
    // $to: Y-m-d
    // $onboardDate: Y-m-d
    // $checkoutDate: Y-m-d
    public function getNumberOfWorkingDays($from, $to, $holiday = [], $unpaidLeaves = [], $onboardDate = null, $checkoutDate = null)
    {
        $exchangeWorkDays = explode(',', env('EXCHANGE_WORK_DAYS')) ?? [];
        $weekendCodes = [0, 6]; # date format = N (1 = Monday, ...)
        $startDate = (isset($onboardDate) && Carbon::createFromDate($onboardDate)->between($from, $to)) ? $onboardDate : $from;
        $endDate = (isset($checkoutDate) && Carbon::createFromDate($checkoutDate)->between($from, $to)) ? $checkoutDate : $to;
        $period = CarbonPeriod::create($startDate, $endDate)->toArray();
        $unpaidLeavesCollection = collect($unpaidLeaves);

        if (isset($checkoutDate) && Carbon::createFromDate($checkoutDate)->lt($from)) return 0;
        if (isset($onboardDate) && Carbon::createFromDate($onboardDate)->gt($to)) return 0;

        $workingDays = collect($period)->filter(function ($date) use ($weekendCodes, $holiday, $unpaidLeavesCollection, $exchangeWorkDays) {
            $ymdDate = $date->format('Y-m-d');
            // is working day if
            // NOT weekend
            // NOT in holidays
            // NOT in any unpaid leave
            // Belong to array exchange work days
            return in_array($ymdDate, $exchangeWorkDays) || (!in_array($date->dayOfWeek, $weekendCodes) &&
                !in_array($ymdDate, $holiday) &&
                $unpaidLeavesCollection->every(function ($unpaidTime) use ($ymdDate) {
                    return $ymdDate < ($unpaidTime->start_date ?? $unpaidTime['start_date']) || ($unpaidTime->end_date ?? $unpaidTime['end_date']) < $ymdDate;
                }));
        })->toArray();
        return count($workingDays);
    }

    public function generateDateRanges($startDate, $endDate)
    {
        $dateRange = array();

        $currentDate = strtotime($startDate);
        $endDate = strtotime($endDate);

        while ($currentDate <= $endDate) {
            $dateRange[] = date('Y-m-d', $currentDate);
            $currentDate = strtotime('+1 day', $currentDate);
        }

        return $dateRange;
    }

    // $from: Y-m-d
    // $to: Y-m-d
    // $onboardDate: Y-m-d
    // $checkoutDate: Y-m-d
    public function getNumberOfWorkingHours($from, $to, $holiday = [], $unpaidLeaves = [], $onboardDate = null, $checkoutDate = null, $numberWorkingHoursPerDay = EBase::WORKING_DAY_HOURS)
    {
        return $this->getNumberOfWorkingDays($from, $to, $holiday, $unpaidLeaves, $onboardDate, $checkoutDate) * $numberWorkingHoursPerDay;
    }

    public function getHoliday()
    {
        $holiday = $this->__getHoliday();
        $holiday = collect($holiday)->map(function ($holiday) {
            $startDate = Carbon::createFromFormat('d/m/Y', $holiday->start_date)->format('Y-m-d');
            $endDate = Carbon::createFromFormat('d/m/Y', $holiday->end_date)->format('Y-m-d');
            $dateRange = CarbonPeriod::create($startDate, $endDate);
            return array_map(fn ($date) => $date->format('Y-m-d'), iterator_to_array($dateRange));
        });
        return collect($holiday)->flatten()->toArray();
    }

    public function getMonthsBetween($start_date, $end_date)
    {
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $start->modify('first day of this month');
        $end->modify('first day of next month');
        $interval = DateInterval::createFromDateString('1 month');
        $period = new DatePeriod($start, $interval, $end);
        $months = [];
        foreach ($period as $month) {
            $months[] = $month->format('Y-m-d');
        }

        return $months;
    }

    public function generateMonthList($fromMonth, $toMonth)
    {
        $months = $this->getMonthsBetween($fromMonth . '-01', $toMonth . '-01');
        return collect($months)->map(function ($firstDayOfMonth) {
            $date = new DateTime($firstDayOfMonth);
            return $date->format('Y-m');
        });
    }

    /**
     * @param $prefix
     * @param array $args
     * @return string
     */
    function createRedisKey($prefix, array $args): string
    {
        // Create a Redis valid key string
        $redisKey = '';
        foreach ($args as $key => $value) {
            $redisKey .= $key . ':' . $value . ':';
        }

        // Remove the trailing colon (if desired)
        $redisKey = rtrim($redisKey, ':');

        $redisKey = str_replace(' ', '_', $redisKey);

        return $prefix . $redisKey;
    }

    function getDatesInRange($startDate, $endDate, $ignoreWeekend = false)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        $dates = [];

        while ($start->lte($end)) {

            if (!$ignoreWeekend) {
                $dates[] = $start->toDateString();
            } else {
                if (!$start->isWeekend()) {
                    $dates[] = $start->toDateString();
                }
            }

            $start->addDay();
        }

        return $dates;
    }
}
