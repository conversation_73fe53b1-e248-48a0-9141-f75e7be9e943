<?php

namespace App\Traits;

use App\Enums\EBase;
use Illuminate\Support\Facades\Cache;

trait HrServiceSupport
{
    use HttpClient;

    public function __getListDivisionByIds($ids = [])
    {
        $urlGetDivisions = env('LINK_HR_SERVICE') . 'list-divisions';
        $response = $this->httpGet($urlGetDivisions, $ids);
        $divisions = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $divisions;
    }

    public function __getDivisions($args = [])
    {
        $urlGetDivisions = env('LINK_HR_SERVICE') . 'divisions';
        $response = $this->httpGet($urlGetDivisions, $args);
        $divisions = isset(json_decode($response)->divisions)
            ? json_decode($response)->divisions
            : [];

        return $divisions;
    }

    public function __getTeams($args = [])
    {
        $urlGetTeams = env('LINK_HR_SERVICE') . 'teams';
        $response = $this->httpGet($urlGetTeams, $args);

        $teams = isset($response) ? json_decode($response) : [];

        return $teams;
    }

    public function __getOvertime($args = [])
    {
        $urlGetOvertimes = env('LINK_HR_SERVICE') . 'overtimes';
        $response = $this->httpGet($urlGetOvertimes, $args);

        return json_decode($response);
    }

    public function __updateDivisionStatus($args, $id)
    {
        $url = env('LINK_HR_SERVICE') . "divisions/$id/update-status";
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __updatePositionStatus($args, $id)
    {
        $url = env('LINK_USER_SERVICE') . "position/$id/update-status";
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function  __requestOpenProjectDl($args)
    {
        $idOpenProjectWF = env('ID_OPEN_PROJECT_DL_WF');
        $url = env('LINK_REQUEST') . "v1/user_request/" . $idOpenProjectWF;
        $args['workflow_id'] = $idOpenProjectWF;
        $response = $this->httpPostWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response);
    }

    public function __getHoliday()
    {
        return Cache::remember('holidays', config('app.cache_expire'), function () {
            $url = env('LINK_HR_SERVICE') . "holiday";
            $response = $this->httpGet($url);

            return json_decode($response)->data;
        });
    }

    public function __getListHoliday($args = [])
    {
        $url = env('LINK_HR_SERVICE') . "list-holiday";
        $response = $this->httpGet($url, $args);

        return json_decode($response)->data;
    }

    public function __getDayLeave($args = [])
    {
        $url = env('LINK_HR_SERVICE') . "day-leave";
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getContractType($args = [])
    {
        $url = env('LINK_HR_SERVICE') . "contract-category";
        $response = $this->httpGet($url, $args);

        return json_decode($response)->data;
    }

    public function __getEmployeeOnLeave($args = [])
    {
        $url = env('LINK_HR_SERVICE') . "division-module/employee-on-leave";
        $response = $this->httpGet($url, $args);
        
        return json_decode($response);
    }
}
