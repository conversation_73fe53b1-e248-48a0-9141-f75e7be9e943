<?php

namespace App\Traits;

trait ProjectServiceSupport
{
    use HttpClient;

    public function addNewMember($id, $args)
    {
        $urlAddMember = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/members';
        $response = $this->httpPost($urlAddMember, $args);

        return json_decode($response);
    }

    public function removeExistMember($userId, $projectId, $id)
    {
        $urlRemoveMember = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/members/' . $id;
        $response = $this->httpDelete($urlRemoveMember, [
            'user_id' => $userId
        ]);

        return json_decode($response);
    }

    public function __getMasterData()
    {
        $urlGetMasterData = env('LINK_PROJECT_SERVICE') . 'master-data';
        $response = $this->httpGet($urlGetMasterData);

        return json_decode($response);
    }

    public function __createProject($args)
    {
        $urlCreateProject = env('LINK_PROJECT_SERVICE') . 'projects';
        $response = $this->httpPost($urlCreateProject, $args);

        return json_decode($response);
    }

    public function __getListProject($args)
    {
        $urlGetProjects = env('LINK_PROJECT_SERVICE') . 'projects';
        $response = $this->httpGet($urlGetProjects, $args);

        return json_decode($response);
    }

    public function __getProject($id, $args = [])
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/members';
        $response = $this->httpGet($urlGetProject, $args);
        $project = json_decode($response);

        return $project;
    }

    public function __getListProjects($args = [])
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'list-project';
        $response = $this->httpPost($urlGetProject, $args);

        return json_decode($response);
    }

    public function __getProjects($args = [])
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'list-project';
        $response = $this->httpPost($urlGetProject, $args);

        return json_decode($response);
    }

    public function __showProject($id, $args = null)
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'projects/' . $id;
        $response = $this->httpGet($urlGetProject, $args);

        return json_decode($response);
    }

    public function __updateProject($args, $id)
    {
        $urlUpdateProject = env('LINK_PROJECT_SERVICE') . 'projects/' . $id;
        $response = $this->httpPut($urlUpdateProject, $args);

        return json_decode($response);
    }

    public function __getAllProject($args)
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'all-project';
        $response = $this->httpGet($urlGetProject, $args);

        return json_decode($response);
    }

    public function __deleteProject($id)
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE')
            . 'projects/' . $id;
        $response = $this->httpDelete($urlGetProject);

        return json_decode($response);
    }

    public function __getCustomers($args)
    {
        $urlGetCustomer = env('LINK_PROJECT_SERVICE') . 'list-customer';
        $response = $this->httpGet($urlGetCustomer, $args);

        return json_decode($response);
    }

    public function __getGeneralInformation($id)
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/general-information';
        $response = $this->httpGet($urlGetProject);

        return json_decode($response);
    }

    public function __getTechnologies($args)
    {
        $urlGetCustomer = env('LINK_PROJECT_SERVICE') . 'technologies';
        $response = $this->httpGet($urlGetCustomer, $args);

        return json_decode($response);
    }

    public function __getListCustomer($args)
    {
        $urlGetCustomer = env('LINK_PROJECT_SERVICE') . 'customers';
        $response = $this->httpGet($urlGetCustomer, $args);

        return json_decode($response);
    }

    public function __createCustomer($args)
    {
        $urlCreateCustomer = env('LINK_PROJECT_SERVICE') . 'customers';
        $response = $this->httpPost($urlCreateCustomer, $args);

        return json_decode($response);
    }

    public function __syncCustomers()
    {
        $urlCreateCustomer = env('LINK_PROJECT_SERVICE') . 'customers-sync';
        $response = $this->httpPost($urlCreateCustomer);

        return json_decode($response);
    }

    public function __updateCustomer($args, $id)
    {
        $urlUpdateCustomer = env('LINK_PROJECT_SERVICE') . 'customers/' . $id;
        $response = $this->httpPut($urlUpdateCustomer, $args);

        return json_decode($response);
    }

    public function __deleteCustomer($id)
    {
        $urlDeleteCustomer = env('LINK_PROJECT_SERVICE') . 'customers/' . $id;
        $response = $this->httpDelete($urlDeleteCustomer);

        return json_decode($response);
    }

    public function __getPcvReports($projectId)
    {
        $urlGetPcvReport = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pcv-reports';
        $response = $this->httpGet($urlGetPcvReport);

        return json_decode($response);
    }

    public function __createPcvReport($args, $projectId)
    {
        $urlCreatePcvReport = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pcv-reports';
        $response = $this->httpPost($urlCreatePcvReport, $args);

        return json_decode($response);
    }

    public function __updatePcvReport($args, $projectId, $pcvReportId)
    {
        $urlUpdatePcvReport = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pcv-reports/' . $pcvReportId;
        $response = $this->httpPut($urlUpdatePcvReport, $args);

        return json_decode($response);
    }

    public function __deletePcvReport($projectId, $pcvReportId)
    {
        $urlDeletePcvReport = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pcv-reports/' . $pcvReportId;
        $response = $this->httpDelete($urlDeletePcvReport);

        return json_decode($response);
    }

    public function __createStage($args, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateStage($args, $projectId, $stageId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteStage($args, $projectId, $stageId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId;
        $response = $this->httpDelete($url, $args);

        return json_decode($response);
    }

    public function __showStage($projectId, $stageId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId;
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __getListStage($args, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getAllocations($projectId, $stageId, $args)
    {
        $urlGetAllocation = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/allocations';
        $response = $this->httpGet($urlGetAllocation, $args);

        return json_decode($response);
    }

    public function __createAllocation($args, $projectId, $stageId)
    {
        $urlCreateAllocation = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/allocations';
        $response = $this->httpPost($urlCreateAllocation, $args);

        return json_decode($response);
    }

    public function __updateAllocation($args, $projectId, $stageId, $allocationId)
    {
        $urlUpdateAllocation = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/allocations/' . $allocationId;
        $response = $this->httpPut($urlUpdateAllocation, $args);

        return json_decode($response);
    }

    public function __deleteAllocation($args, $projectId, $stageId, $allocationId)
    {
        $urlDeleteAllocation = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/allocations/' . $allocationId;
        $response = $this->httpDelete($urlDeleteAllocation, $args);

        return json_decode($response);
    }

    public function __getAllocationByUserIds($args)
    {
        /**
         * parameter too long, so the POST method is required
         */
        $urlGetAllocation = env('LINK_PROJECT_SERVICE') . 'user-allocations';
        $response = $this->httpPost($urlGetAllocation, $args);

        return json_decode($response);
    }

    public function __getDailyReportByUserIds($args)
    {
        /**
         * parameter too long, so the POST method is required
         */
        $urlGetDailyReport = env('LINK_PROJECT_SERVICE') . 'user-daily-reports';
        $response = $this->httpPost($urlGetDailyReport, $args);

        return json_decode($response);
    }

    public function __getAllocationByProjectIds($args)
    {
        $urlGetAllocation = env('LINK_PROJECT_SERVICE') . 'project-allocations';
        $response = $this->httpPost($urlGetAllocation, $args);

        return json_decode($response);
    }

    public function __getDailyReportByProjectIds($args)
    {
        $urlGetDailyReport = env('LINK_PROJECT_SERVICE') . 'project-daily-reports';
        $response = $this->httpPost($urlGetDailyReport, $args);

        return json_decode($response);
    }

    public function __getAllocationInMonth($args)
    {
        $urlGetAllocation = env('LINK_PROJECT_SERVICE') . 'month-allocations';
        $response = $this->httpGet($urlGetAllocation, $args);

        return json_decode($response);
    }

    public function __getAllocationInYear($args)
    {
        $urlGetAllocation = env('LINK_PROJECT_SERVICE') . 'year-allocations';
        $response = $this->httpGet($urlGetAllocation, $args);

        return json_decode($response);
    }

    public function __getListAllocation($args)
    {
        $urlGetAllocation = env('LINK_PROJECT_SERVICE') . 'allocation/list';
        $response = $this->httpGet($urlGetAllocation, $args);

        return json_decode($response);
    }

    public function __createMilestone($args, $projectId, $stageId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/milestones';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateMilestone($args, $projectId, $stageId, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/milestones/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteMilestone($args, $projectId, $stageId, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/milestones/' . $id;
        $response = $this->httpDelete($url, $args);

        return json_decode($response);
    }

    public function __createDeliverable($args, $projectId, $stageId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/deliverables';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateDeliverable($args, $projectId, $stageId, $deliverableId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/deliverables/' . $deliverableId;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteDeliverable($args, $projectId, $stageId, $deliverableId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/deliverables/' . $deliverableId;
        $response = $this->httpDelete($url, $args);

        return json_decode($response);
    }

    public function __getWeeks($projectId)
    {
        $urlGetWeek = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/weeks';
        $response = $this->httpGet($urlGetWeek);

        return json_decode($response);
    }

    public function __getRoles($args = [])
    {
        $urlGetWeek = env('LINK_PROJECT_SERVICE') . 'roles';
        $response = $this->httpGet($urlGetWeek, $args);

        return json_decode($response);
    }

    public function __getSchedule($projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/schedule';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __createRequirementChange($args, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/requirement-change';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateRequirementChange($args, $projectId, $requirementChangeId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/requirement-change/' . $requirementChangeId;
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __deleteRequirementChange($projectId, $args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/requirement-change/';
        $response = $this->httpDelete($url, $args);

        return json_decode($response);
    }

    public function __showRequirementChange($projectId, $requirementChangeId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/requirement-change/' . $requirementChangeId;
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __getListRequirementChange($args, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/requirement-change';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __importRequirementChange($dataImport, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/requirement-change/import';
        $response = $this->httpPost($url, $dataImport);

        return json_decode($response);
    }

    public function __getPositionProjectDB()
    {
        $urlGetPosition = env('LINK_PROJECT_SERVICE') . 'roles';
        $response = $this->httpGet($urlGetPosition);
        $positions = isset(json_decode($response)->data)
            ? json_decode($response)->data
            : [];

        return $positions;
    }

    public function __getListPmReport($args, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pm-report';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getMemberIdsByRoles($projectId, $role_names)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/members-by-roles';
        $response = $this->httpGet($url, ['role_names' => $role_names]);

        return json_decode($response);
    }

    public function __getDateReported($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/pm-reported';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __createPmReport($args, $projectId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pm-report';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updatePmReport($args, $projectId, $pmReportId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pm-report/' . $pmReportId;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __showPmReport($projectId, $pmReportId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pm-report/' . $pmReportId;
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __deletePmReport($projectId, $pmReportId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/pm-report/' . $pmReportId;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __getActivityLogs($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'log-activities/' . $id;
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __showActivityLog($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'log-activities/' . $id;
        $response = $this->httpGet($url, $id);

        return json_decode($response);
    }

    public function __getListPosition($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'positions';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __createPosition($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'positions';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updatePosition($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'positions/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __showPosition($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'positions/' . $id;
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __deletePosition($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'positions/' . $id;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __getListDivision($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'divisions';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __overviewProject($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/overview';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __expenseEE($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/expense-ee';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __getListLevel($args)
    {
        $url = env('LINK_USER_SERVICE') . 'levels';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __createLevel($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'levels';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateLevel($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'levels/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteLevel($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'levels/' . $id;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __updateRole($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'roles/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __getListDeliverable($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'deliverables/' . $id;
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __getListMilestone($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'milestones/' . $id;
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __userBelongsToProject($userIds)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'user-belong-to-project';
        $response = $this->httpGet($url, $userIds);

        return json_decode($response);
    }

    public function __getPmReports($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'pm-reports';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getMembers($id, $args = [])
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/members';

        return  $this->httpGet($urlGetProject, $args)->json()['user_role'];
    }

    public function __getListUserUnpaidLeave($args = [])
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'user-unpaid-leave';
        $response = $this->httpGet($urlGetProject, $args);

        return json_decode($response);
    }

    public function __getUserUnpaidLeaveByTime($args = [])
    {
        $urlGetProject = env('LINK_PROJECT_SERVICE') . 'user-unpaid-leave-by-time';
        $response = $this->httpGet($urlGetProject, $args);

        return json_decode($response);
    }

    public function __createUserUnpaidLeave($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'user-unpaid-leave';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateUserUnpaidLeave($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'user-unpaid-leave/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteUserUnpaidLeave($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'user-unpaid-leave/' . $id;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __getListDailyReport($args = [])
    {
        $url = env('LINK_PROJECT_SERVICE') . 'daily-reports';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getListReportOfUser($args = [])
    {
        $url = env('LINK_PROJECT_SERVICE') . 'user-daily-reports';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getProjectsOfUser($args = [])
    {
        $url = env('LINK_PROJECT_SERVICE') . 'user-projects';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __changeStatus($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'change-status-daily-reports';

        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __getProjectBudget($args, $id)
    {
        $urlGetBudget = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/budget';
        $response = $this->httpGet($urlGetBudget, $args);

        return json_decode($response);
    }

    public function __updateProjectBudget($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/budget';
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __getEEChartData($args, $id)
    {
        $urlGetEEChartData = env('LINK_PROJECT_SERVICE') . 'projects/' . $id . '/data-ee-chart';
        $response = $this->httpGet($urlGetEEChartData, $args);

        return json_decode($response);
    }

    public function __getReports($args)
    {
        $urlGetReports = env('LINK_PROJECT_SERVICE') . 'user-daily-reports';
        $response = $this->httpGet($urlGetReports, $args);

        return json_decode($response);
    }

    public function __getProjectMembers($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'project-members';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __cloneStage($args, $projectId, $stageId)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'projects/' . $projectId . '/stages/' . $stageId . '/clone';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __getProjectStatistics($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'project-statistics';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getProjectBudgets($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'project-budgets';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getChargeRateStatistics($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'charge-rate-statistics';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getResourceRentalCosts($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . "resource-rental-costs";
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __updateOrCreateResourceRentalCost($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . "resource-rental-costs";
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __deleteResourceRentalCost($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . "resource-rental-costs/$id";
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __genListRentalCost($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . "resource-rental-costs";
        $response = $this->httpGet($url, $args);

        return json_decode($response, true);
    }

    public function __getProjectsOfUserRolePM($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'worklog-report';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getProjectByCode($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'project-by-code';
        $response = $this->httpGet($url, $args);

        return json_decode($response)->project;
    }

    public function __getProjectMonthBudget($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'project-month-budget';
        $response = $this->httpGet($url, $args);

        return json_decode($response)->budget;
    }

    public function __getAllocates($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'allocation/list';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __updateTasks($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'tasks';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __getListTask($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'list-task';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __getRules()
    {
        $url = env('LINK_PROJECT_SERVICE') . 'rules';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __createRule($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'rules';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateRule($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'rules/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteRule($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'rules/' . $id;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __getPenalties()
    {
        $url = env('LINK_PROJECT_SERVICE') . 'penalties';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __createPenalty($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'penalties';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updatePenalty($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'penalties/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deletePenalty($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'penalties/' . $id;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __getRulePenalties()
    {
        $url = env('LINK_PROJECT_SERVICE') . 'rule-penalties';
        $response = $this->httpGet($url);

        return json_decode($response);
    }

    public function __updateRulePenalties($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'rule-penalties';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __getViolationReport($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'violation-reports';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __createViolationReport($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'violation-reports';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateViolationReport($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'violation-reports/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteViolationReport($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'violation-reports/' . $id;
        $response = $this->httpDelete($url, $args);

        return json_decode($response);
    }

    public function __getViolationReportActivityLogs($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'log-activities';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __createWeeklyReport($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'weekly-reports';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }

    public function __updateWeeklyReport($args, $id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'weekly-reports/' . $id;
        $response = $this->httpPut($url, $args);

        return json_decode($response);
    }

    public function __deleteWeeklyReport($id)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'weekly-reports/' . $id;
        $response = $this->httpDelete($url);

        return json_decode($response);
    }

    public function __getWeeklyReports($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'weekly-reports';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getRawProjects($args)
    {
        $url = env('LINK_PROJECT_SERVICE') . 'raw-projects';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __getProjectPerformance($id, $args = null)
    {
        $url = config('services.project.domain') . 'projects/' . $id . '/performance';
        $response = $this->httpGet($url, $args);

        return json_decode($response);
    }

    public function __importProjectPerformance($id, $args = [])
    {
        $url = config('services.project.domain') . 'projects/' . $id . '/performance/import';
        $response = $this->httpPost($url, $args);

        return json_decode($response);
    }
}
