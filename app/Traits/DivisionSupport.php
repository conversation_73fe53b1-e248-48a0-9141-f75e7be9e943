<?php

namespace App\Traits;

trait DivisionSupport
{
    use HttpClient;

    public function __getDivisionChargeRates($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'division-charge-rate';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['charge_rates'] ?? [];
    }

    public function __getDivisionManMonthList($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'division-man-month-list';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['division_man_month_list'] ?? [];
    }
    

    public function __getRentalResources($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'rental-resources';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['rental_resources'] ?? [];
    }

    public function __getIdleWorkforce($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'hr/idle-workforce';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['resources'] ?? [];
    }

    public function __getIdleWorkforceDivisionStats($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'idle-workforce/division-statistic';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['data'] ?? [];
    }

    public function __getIdleWorkforcePositionSkillStats($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'idle-workforce/position-skill-statistic';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['data'] ?? [];
    }

    public function __getIdleWorkforceMonthStats($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'idle-workforce/month-statistic';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);

        return json_decode($response, true)['data'] ?? [];
    }

    public function __getAvailableEmployee($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'hr/available-employees';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);
        
        return json_decode($response, true)['available_employees'] ?? [];
    }

    public function __getStaffCoefficients($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'staff-coefficients';

        $response = $this->httpGetWithToken(request()->bearerToken(), $url, $args);
        
        return json_decode($response, true)['staff_coefficients'];
    }

    public function __createStaffCoefficient($args = [])
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'staff-coefficients';

        $response = $this->httpPostWithToken(request()->bearerToken(), $url, $args);
        
        return json_decode($response, true);
    }

    public function __updateStaffCoefficient($args, $id)
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'staff-coefficients/' . $id;

        $response = $this->httpPutWithToken(request()->bearerToken(), $url, $args);
        
        return json_decode($response, true);
    }

    public function __deleteStaffCoefficient($id)
    {
        $url = env('LINK_DIVISION_PRESENTATION') . 'staff-coefficients/' . $id;

        $response = $this->httpDeleteWithToken(request()->bearerToken(), $url);
        
        return $response;
    }
}
