<?php

namespace App\Traits;

use App\Enums\ERole;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

trait Helper
{
    public function decodeAccessToken($accessToken)
    {
        try {
            return JWT::decode($accessToken, new Key(env('JWT_SECRET'), env('JWT_ALGO')));
        } catch (SignatureInvalidException $exception) {
            return unauthorized(__('signature_verification_failed'), []);
        } catch (BeforeValidException $exception) {
            return unauthorized($exception->getMessage(), []);
        } catch (ExpiredException $exception) {
            return unauthorized(__('expired_token'), []);
        } catch (\Exception $exception) {
            return unauthorized(__('unauthorize'), []);
        }
    }

    public function getUserIdFromAccessToken($accessToken)
    {
        $decoded = $this->decodeAccessToken($accessToken);

        return $decoded->user_id;
    }

    public function getUserInfoAccessToken($accessToken)
    {
        return $this->decodeAccessToken($accessToken);
    }

    public function getCurrentUserInfo()
    {
        return $this->decodeAccessToken(request()->bearerToken());
    }

    public function removeStringSpace($str)
    {
        return trim(preg_replace('/\s\s+/', ' ', $str));
    }

    public function isUserInRoles($roles): bool
    {
        $user = $this->getCurrentUserInfo();
        $userRoles = $user->position->name;

        return in_array($userRoles, $roles);
    }

    public function isSystemAdmin(): bool
    {
        return in_array($this->getCurrentUserInfo()->user_id, explode(',', env('ADMIN_USER_IDS')));
    }

    public function isDLRole()
    {
        return $this->isUserInRoles(ERole::GROUP_DL);
    }
}
