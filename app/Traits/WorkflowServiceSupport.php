<?php

namespace App\Traits;

trait WorkflowServiceSupport
{
    use HttpClient;

    public function __getRequestProject($accessToken, $args = [])
    {
        $urlGetRqProject = env('LINK_REQUEST').'v1/projects';
        $response = $this->httpGetWithToken($accessToken, $urlGetRqProject, $args);
        $projects = isset(json_decode($response)->projects)
            ? json_decode($response)->projects
            : [];

        return $projects;
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = (new ValidationException($validator))->errors();
        throw new HttpResponseException(response()->json(
            [
                'messages' => $errors,
                'status' => 422,
            ],
            Response::HTTP_UNPROCESSABLE_ENTITY
        ));
    }
}
