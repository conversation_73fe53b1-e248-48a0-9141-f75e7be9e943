<?php

namespace App\Enums;

class ERequirementChangeStatus
{
    public const COMMITTED = 1;

    public const CONFIRMING = 2;

    public const DONE = 3;

    public const CANCEL = 4;

    public const PAID = 5;

    public static function getAll()
    {
        return [
            self::COMMITTED,
            self::CONFIRMING,
            self::DONE,
            self::CANCEL,
            self::PAID,
        ];
    }

    public static function getMasterData()
    {
        return [
            'committed' => self::COMMITTED,
            'confirming' => self::CONFIRMING,
            'done' => self::DONE,
            'cancel' => self::CANCEL,
            'paid' => self::PAID,
        ];
    }

    public static function getActionString($value)
    {
        switch ($value) {
            case ERequirementChangeStatus::COMMITTED:
                return 'Committed';
            case ERequirementChangeStatus::CONFIRMING:
                return 'Confirming';
            case ERequirementChangeStatus::DONE:
                return 'Done';
            case ERequirementChangeStatus::CANCEL:
                return 'Cancel';
            case ERequirementChangeStatus::PAID:
                return 'Paid';
        }

        return null;
    }
}
