<?php

namespace App\Enums;

class ERole
{
    const BOD = 'BoD';

    const BOM = 'BoM';

    const PM = 'PM';

    const DEVELOPER = 'Developer';

    const BA = 'BA';

    const TECH_LEAD = 'Tech Lead';

    const TEST_LEAD = 'Test Lead';

    const COMTOR = 'Comtor';

    const BRSE = 'BrSE';

    const TESTER = 'Tester';

    const PQA = 'PQA';

    const DESIGNER = 'Designer';

    const DL = 'Division Leader';

    const SUB_DL = 'Vice Division Leader';

    const AL = 'Amoeba Leader';

    const GROUP_DL = [
        self::DL,
        self::SUB_DL,
        self::AL
    ];

    const GROUP_DL_IDS = [17, 25, 46];
    
    const SUB_PM = 'Sub-PM';

    const SCRUM_MASTER = 'Scrum Master';

    const PRODUCT_OWNER = 'Product Owner';

    const SALES = 'Sales';

    const SYSTEM_ADMIN = 'Admin';

    const CEO = 'CEO';

    const CTO = 'CTO';

    const COO = 'COO';

    const PMO = 'PMO';

    const INTERN = 'Intern';
}
