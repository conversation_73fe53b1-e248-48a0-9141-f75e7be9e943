<?php

namespace App\Enums;

class ERequirementChangeImport
{
    public const ERROR_EMPTY_DATA = 1;

    public const ERROR_INVALID_FORMAT = 2;

    public const FILE_VALID_FORMAT = 3;

    public const BEGIN_ROW = 2;

    public const COLUMN_NO = 'A';

    public const COLUMN_TYPE = 'B';

    public const COLUMN_REQUEST_DATE = 'C';

    public const COLUMN_TITLE = 'D';

    public const COLUMN_CONTENT = 'E';

    public const COLUMN_DETAILED_CONTENT = 'F';

    public const COLUMN_IMPACT = 'G';

    public const COLUMN_IMPACT_ROLES = 'H';

    public const COLUMN_STATUS = 'I';

    public const COLUMN_STAGE = 'J';

    public const COLUMN_COST = 'K';

    public const COLUMN_NOTE = 'L';
}
