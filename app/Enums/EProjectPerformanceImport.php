<?php

namespace App\Enums;

class EProjectPerformanceImport
{
    public const MASTER_DATA_SHEET = 0;

    public const FUNCTION_SHEET = 1;

    // Sprint
    public const COLUMN_SPRINT_NAME = 0;

    public const COLUMN_SPRINT_START_DATE = 1;

    public const COLUMN_SPRINT_END_DATE = 2;

    // Defect
    // Internal
    public const COLUMN_DEFECT_INTERNAL_CRITICAL = 3;

    public const COLUMN_DEFECT_INTERNAL_MAJOR = 4;

    public const COLUMN_DEFECT_INTERNAL_NORMAL = 5;

    public const COLUMN_DEFECT_INTERNAL_LOW = 6;

    // External
    public const COLUMN_DEFECT_EXTERNAL_CRITICAL = 7;

    public const COLUMN_DEFECT_EXTERNAL_MAJOR = 8;

    public const COLUMN_DEFECT_EXTERNAL_NORMAL = 9;

    public const COLUMN_DEFECT_EXTERNAL_LOW = 10;

    // Leakage
    public const COLUMN_DEFECT_LEAKAGE_CRITICAL = 11;

    public const COLUMN_DEFECT_LEAKAGE_MAJOR = 12;

    public const COLUMN_DEFECT_LEAKAGE_NORMAL = 13;

    public const COLUMN_DEFECT_LEAKAGE_LOW = 14;

    // Quality gate
    public const COLUMN_QUALITY_GATE_WEIGHTED_NON_COMPLIANCE = 15;

    public const COLUMN_QUALITY_GATE_PROCESS_APPLY = 16;

    public const COLUMN_QUALITY_GATE_NUMBER_OF_INCIDENT = 17;

    public const COLUMN_QUALITY_GATE_NUMBER_OF_CUSTOMER_COMPLAINT = 18;

    public const COLUMN_QUALITY_GATE_QUALITY_GATE = 19;

    // Function
    public const COLUMN_FUNCTION_CATEGORY = 0;

    public const COLUMN_FUNCTION_NAME = 2;

    public const COLUMN_FUNCTION_SPRINT = 3;

    public const COLUMN_FUNCTION_STORY_POINT = 4;

    public const COLUMN_FUNCTION_ESTIMATE = 5;

    public const COLUMN_FUNCTION_WORK_COMPLETED = 6;

    public const COLUMN_NAMES = [
        self::MASTER_DATA_SHEET => [
            self::COLUMN_SPRINT_NAME => 'name',
            self::COLUMN_SPRINT_START_DATE => 'start_date',
            self::COLUMN_SPRINT_END_DATE => 'end_date',
            self::COLUMN_DEFECT_INTERNAL_CRITICAL => 'total',
            self::COLUMN_DEFECT_INTERNAL_MAJOR => 'total',
            self::COLUMN_DEFECT_INTERNAL_NORMAL => 'total',
            self::COLUMN_DEFECT_INTERNAL_LOW => 'total',
            self::COLUMN_DEFECT_EXTERNAL_CRITICAL => 'total',
            self::COLUMN_DEFECT_EXTERNAL_MAJOR => 'total',
            self::COLUMN_DEFECT_EXTERNAL_NORMAL => 'total',
            self::COLUMN_DEFECT_EXTERNAL_LOW => 'total',
            self::COLUMN_DEFECT_LEAKAGE_CRITICAL => 'total',
            self::COLUMN_DEFECT_LEAKAGE_MAJOR => 'total',
            self::COLUMN_DEFECT_LEAKAGE_NORMAL => 'total',
            self::COLUMN_DEFECT_LEAKAGE_LOW => 'total',
            self::COLUMN_QUALITY_GATE_WEIGHTED_NON_COMPLIANCE => 'number_of_non_compliance',
            self::COLUMN_QUALITY_GATE_PROCESS_APPLY => 'number_of_process',
            self::COLUMN_QUALITY_GATE_NUMBER_OF_INCIDENT => 'number_of_incident',
            self::COLUMN_QUALITY_GATE_NUMBER_OF_CUSTOMER_COMPLAINT => 'number_of_customer_complaint',
            self::COLUMN_QUALITY_GATE_QUALITY_GATE => 'quality_gate',
        ],
        self::FUNCTION_SHEET => [
            self::COLUMN_FUNCTION_CATEGORY => 'function_category',
            self::COLUMN_FUNCTION_NAME => 'name',
            self::COLUMN_FUNCTION_SPRINT => 'sprint',
            self::COLUMN_FUNCTION_STORY_POINT => 'story_point',
            self::COLUMN_FUNCTION_ESTIMATE => 'estimate',
            self::COLUMN_FUNCTION_WORK_COMPLETED => 'work_completed',
        ],
    ];

    public const SPRINT_COLUMNS = [
        self::COLUMN_SPRINT_NAME,
        self::COLUMN_SPRINT_START_DATE,
        self::COLUMN_SPRINT_END_DATE,
    ];

    public const DEFECT_COLUMNS = [
        self::COLUMN_DEFECT_INTERNAL_CRITICAL => [
            'type' => EDefect::INTERNAL,
            'severity' => EDefect::CRITICAL_SEVERITY,
        ],
        self::COLUMN_DEFECT_INTERNAL_MAJOR => [
            'type' => EDefect::INTERNAL,
            'severity' => EDefect::MAJOR_SEVERITY,
        ],
        self::COLUMN_DEFECT_INTERNAL_NORMAL => [
            'type' => EDefect::INTERNAL,
            'severity' => EDefect::NORMAL_SEVERITY,
        ],
        self::COLUMN_DEFECT_INTERNAL_LOW => [
            'type' => EDefect::INTERNAL,
            'severity' => EDefect::LOW_SEVERITY,
        ],
        self::COLUMN_DEFECT_EXTERNAL_CRITICAL => [
            'type' => EDefect::EXTERNAL,
            'severity' => EDefect::CRITICAL_SEVERITY,
        ],
        self::COLUMN_DEFECT_EXTERNAL_MAJOR => [
            'type' => EDefect::EXTERNAL,
            'severity' => EDefect::MAJOR_SEVERITY,
        ],
        self::COLUMN_DEFECT_EXTERNAL_NORMAL => [
            'type' => EDefect::EXTERNAL,
            'severity' => EDefect::NORMAL_SEVERITY,
        ],
        self::COLUMN_DEFECT_EXTERNAL_LOW => [
            'type' => EDefect::EXTERNAL,
            'severity' => EDefect::LOW_SEVERITY,
        ],
        self::COLUMN_DEFECT_LEAKAGE_CRITICAL => [
            'type' => EDefect::LEAKAGE,
            'severity' => EDefect::CRITICAL_SEVERITY,
        ],
        self::COLUMN_DEFECT_LEAKAGE_MAJOR => [
            'type' => EDefect::LEAKAGE,
            'severity' => EDefect::MAJOR_SEVERITY,
        ],
        self::COLUMN_DEFECT_LEAKAGE_NORMAL => [
            'type' => EDefect::LEAKAGE,
            'severity' => EDefect::NORMAL_SEVERITY,
        ],
        self::COLUMN_DEFECT_LEAKAGE_LOW => [
            'type' => EDefect::LEAKAGE,
            'severity' => EDefect::LOW_SEVERITY,
        ],
    ];

    public const QUALITY_GATE_COLUMNS = [
        self::COLUMN_QUALITY_GATE_WEIGHTED_NON_COMPLIANCE,
        self::COLUMN_QUALITY_GATE_PROCESS_APPLY,
        self::COLUMN_QUALITY_GATE_NUMBER_OF_INCIDENT,
        self::COLUMN_QUALITY_GATE_NUMBER_OF_CUSTOMER_COMPLAINT,
        self::COLUMN_QUALITY_GATE_QUALITY_GATE,
    ];

    public const FUNCTION_COLUMNS = [
        self::COLUMN_FUNCTION_CATEGORY,
        self::COLUMN_FUNCTION_NAME,
        self::COLUMN_FUNCTION_SPRINT,
        self::COLUMN_FUNCTION_STORY_POINT,
        self::COLUMN_FUNCTION_ESTIMATE,
        self::COLUMN_FUNCTION_WORK_COMPLETED,
    ];

    const FORMAT_DATE_COLUMNS = [
        self::MASTER_DATA_SHEET => [
            self::COLUMN_SPRINT_START_DATE,
            self::COLUMN_SPRINT_END_DATE,
        ],
    ];

    const CONVERT_TO_INT_COLUMNS = [
        self::MASTER_DATA_SHEET => [
            self::COLUMN_QUALITY_GATE_QUALITY_GATE => EQualityGate::QUALITY_GATE_LABELS,
        ],
        self::FUNCTION_SHEET => [
            self::COLUMN_FUNCTION_WORK_COMPLETED => EFunction::WORK_COMPLETED_LABELS,
        ],
    ];

    public static function generateDefect($column, $value): array
    {
        return collect(self::DEFECT_COLUMNS[$column])
            ->merge([self::COLUMN_NAMES[self::MASTER_DATA_SHEET][$column] => $value])
            ->toArray();
    }

    public static function generateColumn($sheet, $column, $value): array
    {
        if (in_array($column, @self::FORMAT_DATE_COLUMNS[$sheet] ?? [])) {
            $value = formatDateFromExcelValue($value);
        }

        if (in_array(
            $column,
            array_keys(@self::CONVERT_TO_INT_COLUMNS[$sheet] ?? []))
        ) {
            $labelToValue = array_flip(@self::CONVERT_TO_INT_COLUMNS[$sheet][$column] ?? []);
            $value = @$labelToValue[$value] ?? $value;
        }

        return [
            self::COLUMN_NAMES[$sheet][$column] => $value,
        ];
    }
}
