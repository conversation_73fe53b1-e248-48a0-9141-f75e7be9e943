<?php

namespace App\Enums;

class ERequirementChangeType
{
    public const FEE = 1;

    public const BONUS = 2;

    public static function getAll()
    {
        return [
            self::FEE,
            self::BONUS,
        ];
    }

    public static function getMasterData()
    {
        return [
            'Fee' => self::FEE,
            'Bonus' => self::BONUS,
        ];
    }

    public static function getActionString($value)
    {
        switch ($value) {
            case ERequirementChangeType::FEE:
                return 'Fee';
            case ERequirementChangeType::BONUS:
                return 'Bonus';
        }

        return null;
    }
}
