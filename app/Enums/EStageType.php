<?php

namespace App\Enums;

class EStageType
{
    public const SPRINT = 1;

    public const LABO = 2;

    public const SOLUTION = 3;

    public const CONSTRUCTION = 4;

    public const TRANSITION_AND_TERMINATION = 5;

    public const GUARANTEE = 6;

    public static function getAll()
    {
        return [
            self::SPRINT,
            self::LABO,
            self::SOLUTION,
            self::CONSTRUCTION,
            self::TRANSITION_AND_TERMINATION,
            self::GUARANTEE,
        ];
    }

    /**
     * has also a EStageType in service
     */
    public static function getMasterData()
    {
        return [
            'sprint' => self::SPRINT,
            'labo' => self::LABO,
            'solution' => self::SOLUTION,
            'construction' => self::CONSTRUCTION,
            'transition_and_termination' => self::TRANSITION_AND_TERMINATION,
            'guarantee' => self::GUARANTEE,
        ];
    }

    public static function getActionString($value)
    {
        switch ($value) {
            case EStageType::SPRINT:
                return 'Sprint';
            case EStageType::LABO:
                return 'Labo';
            case EStageType::SOLUTION:
                return 'Solution';
            case EStageType::CONSTRUCTION:
                return 'Construction';
            case EStageType::TRANSITION_AND_TERMINATION:
                return 'Transition And Termination';
            case EStageType::GUARANTEE:
                return 'Guarantee';
        }

        return null;
    }
}
