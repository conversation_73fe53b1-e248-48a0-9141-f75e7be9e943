<?php

namespace App\Enums;

class EProjectType
{
    public const BY_CUSTOMER = 1;
    public const IN_HOUSE = 2;
    public const TRAINING = 3;
    public const OPPORTUNITY = 4;

    public const MANUFACTURING = 1;
    public const RETAIL = 2;
    public const FINANCIAL_SERVICES = 3;
    public const EDUCATION = 4;
    public const HEALTHCARE = 5;
    public const SCIENCE_AND_TECHNOLOGY = 6;
    public const TOURISM_AND_ENTERTAINMENT = 7;
    public const AGRICULTURE_AND_FOOD = 8;
    public const OTHER = 9;
    public const CONSTRUCTION = 10;
    public const ECOMMERCE = 11;

    const OPEN = 1;
    const IN_PROGRESS = 2;
    const PENDING = 3;
    const CANCELED = 4;
    const GUARANTEE = 5;
    const CLOSED = 6;

    public static function getActionString($value)
    {
        switch ($value) {
            case EProjectType::BY_CUSTOMER:
                return 'by customer';
            case EProjectType::IN_HOUSE:
                return 'in-house';
            case EProjectType::TRAINING:
                return 'training';
            case EProjectType::OPPORTUNITY:
                return 'opportunity';
        }

        return null;
    }

    public static function getStatus($value)
    {
        switch ($value) {
            case EProjectType::OPEN:
                return 'open';
            case EProjectType::IN_PROGRESS:
                return 'in progress';
            case EProjectType::PENDING:
                return 'pending';
            case EProjectType::CANCELED:
                return 'canceled';
            case EProjectType::GUARANTEE:
                return 'guarantee';
            case EProjectType::CLOSED:
                return 'closed';
        }

        return null;
    }

    public static function getIndustry($value): ?string
    {
        return match ($value) {
            self::MANUFACTURING => 'Sản xuất và chế tạo',
            self::RETAIL => 'Bán lẻ',
            self::FINANCIAL_SERVICES => 'Dịch vụ tài chính',
            self::EDUCATION => 'Giáo dục',
            self::HEALTHCARE => 'Y tế',
            self::SCIENCE_AND_TECHNOLOGY => 'Khoa học và công nghệ',
            self::TOURISM_AND_ENTERTAINMENT => 'Du lịch và giải trí',
            self::AGRICULTURE_AND_FOOD => 'Nông nghiệp và thực phẩm',
            self::OTHER => 'Other',
            self::CONSTRUCTION => 'Xây dựng',
            self::ECOMMERCE => 'Thương mại điện tử',
            default => null,
        };
    }
}
