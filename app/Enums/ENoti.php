<?php

namespace App\Enums;

class ENoti
{
    const SYSTEM_PROJECT = 6;

    const ADD_MEMBER_TYPE = 1;

    const UPDATE_PROJECT_TYPE = 2;

    const APPROVE_PROJECT_TYPE = 3;

    const PCV_REPORT_TYPE = 4;

    const PM_REPORT_TYPE = 5;

    const REMOVE_MEMBER_TYPE = 6;

    const URI_PATHS = [
        self::ADD_MEMBER_TYPE => '/projects/detail/:id/activity-log',
        self::REMOVE_MEMBER_TYPE => '/projects/detail/:id/activity-log',
        self::UPDATE_PROJECT_TYPE => '/projects/detail/:id/activity-log',
        self::APPROVE_PROJECT_TYPE => '/projects/detail/:id',
        self::PCV_REPORT_TYPE => '/projects',
        self::PM_REPORT_TYPE => '/projects/detail/:id/pm-report',
    ];

    public static function getDetailNoti($id, $type)
    {
        $path = self::URI_PATHS[$type];
        $path = __($path, ['id' => $id]);

        return [
            'path' => $path,
        ];
    }
}
