<?php

namespace App\Enums;

class EFunction
{
    const NOT_AVAILABLE_WORK_COMPLETED = 0;
    const CLOSED_WORK_COMPLETED = 1;
    const CODE_DONE_WORK_COMPLETED = 2;
    const TEST_DONE_WORK_COMPLETED = 3;

    const WORK_COMPLETED_LABELS = [
        self::NOT_AVAILABLE_WORK_COMPLETED => 'N/A',
        self::CLOSED_WORK_COMPLETED => 'Closed',
        self::CODE_DONE_WORK_COMPLETED => 'Done code',
        self::TEST_DONE_WORK_COMPLETED => 'Done test',
    ];

    const EARNED_VALUE_COEFFICIENTS = [
        self::NOT_AVAILABLE_WORK_COMPLETED => 0,
        self::CLOSED_WORK_COMPLETED => 1,
        self::CODE_DONE_WORK_COMPLETED => 0.6,
        self::TEST_DONE_WORK_COMPLETED => 0.8,
    ];
}
