<?php

namespace App\Enums;

class EWeeklyReport
{
    public const COLUMNS_EXPORT = [
        "project_code" => "Project Code",
        "project_name" => "Project Name",
        "customer_name" => "Customer's Name",
        "pm_name" => "PM Name",
        "dm_name" => "DM Name",
        "pqa_name" => "PQA Name",
        "pic_name" => "PIC Name",
        "stage_type" => "Giai đoạn",
        "pm_status" => "PM Status",
        "pm_note" => "PM View",
        "dm_status" => "DM Status",
        "dm_note" => "DM View",
        "pqa_status" => "PQA Status",
        "pqa_note" => "PQA View",
        "pic_status" => "PIC Status",
        "pic_note" => "PIC View",
    ];

    public const COLUMNS_EXPORT_PROJECTS = [
        "division_name" => "Division Name",
        "project_name" => "Project Name",
        "customer_name" => "Customer's Name",
        "pm_name" => "PM Name",
        "pm_status" => "PM Status",
        "dm_status" => "DM Status",
        "pqa_status" => "PQA Status",
        "pic_status" => "PIC Status",
        "action_content" => "PMO Action",
        "action_name" => "PIC",
        "action_status" => "Status",
    ];

    public const FILE_NAME_EXPORT = 'weekly_reports.xlsx';

    public const SOLUTION = 1;
    public const CONSTRUCTION = 2;
    public const MAINTAIN = 3;
    public const UAT = 4;
    public const CLOSING = 5;

    public static function getStageType()
    {
        return [
            self::SOLUTION => 'Solution',
            self::CONSTRUCTION => 'Construction',
            self::MAINTAIN => 'Maintain',
            self::UAT => 'UAT',
            self::CLOSING => 'Closing',
        ];
    }

    public const RED = 1;
    public const AMBER = 2;
    public const GREEN = 3;
    public const NA = 4;

    public static function getStatus()
    {
        return [
            self::RED => 'Red',
            self::AMBER => 'Amber',
            self::GREEN => 'Green',
            self::NA => 'N/A',
        ];
    }

    public const OPEN = 1;
    public const INPROGESS = 2;
    public const CLOSE = 3;

    public static function getStatusAction()
    {
        return [
            self::OPEN => 'Open',
            self::INPROGESS => 'Inprogess',
            self::CLOSE => 'Close',
        ];
    }
}
