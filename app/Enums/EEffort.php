<?php

namespace App\Enums;

class EEffort
{
    public const MORE_THAN_120 = 1;

    public const BETWEEN_81_120 = 2;

    public const BETWEEN_1_80 = 3;

    public const ZERO = 4;

    public static function getAll()
    {
        return [
            self::MORE_THAN_120,
            self::BETWEEN_81_120,
            self::BETWEEN_1_80,
            self::ZERO,
        ];
    }

    public static function getMasterData()
    {
        return [
            'more_than_120' => self::MORE_THAN_120,
            'between_81_120' => self::BETWEEN_81_120,
            'between_1_80' => self::BETWEEN_1_80,
            'zero' => self::ZERO,
        ];
    }
}
