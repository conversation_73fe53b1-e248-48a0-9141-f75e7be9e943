<?php

use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Shared\Date;

/**
 * Return a Carbon instance.
 */
function carbon(string $parseString = '', string $tz = null): Carbon
{
    try {
        return new Carbon($parseString, $tz);
    } catch (\Exception $e) {
    }
}

/**
 * Return a formatted Carbon date.
 */
function humanize_date(Carbon $date, string $format = \App\Constants\App::DATETIME_FORMAT): string
{
    return $date ? $date->format($format) : null;
}

function is_new($created_at): bool
{
    return strtotime($created_at) > strtotime('-1 weeks');
}

function day_of_week($dateString = null): int
{
    /**
     * Need to minus 1 cause of conflict between PHP and Lambda
     * PHP start with 1 for monday by ISO 8601
     * Lambda value start monday by 0
     */
    return (empty($dateString) ? date('N') : date('N', strtotime($dateString))) - 1;
}

/**
 * @param $start_time
 * @param $duration
 * @return string
 */
function get_end_time($start_time, $duration): string
{
    return Carbon::make($start_time)->addMinutes($duration)->format(\App\Constants\App::TIME_FORMAT);
}

function get_date_diff($start_time, $end_time): string
{
    if (! $start_time || ! $end_time) {
        return 0;
    }
    try {
        return Carbon::parse($start_time)->diffInDays(Carbon::parse($end_time));
    } catch(Exception $e) {
        return 0;
    }
}

function get_days($startTime, $endTime)
{
    $start = Carbon::parse($startTime);
    $end = Carbon::parse($endTime);

    $listDates = [];
    $days = $start->diffInDaysFiltered(function (Carbon $date) use (&$listDates) {
        $listDates[] = $date;

        return true;
    }, $end);

    return [$listDates, $days];
}

function get_duration($start_time, $end_time)
{
    return Carbon::parse($end_time)->diffInMinutes(Carbon::parse($start_time));
}

function get_diff_day($startTime, $endTime, $holidays = [])
{
    $start = Carbon::parse($startTime)->startOfDay();
    $end = Carbon::parse($endTime)->startOfDay();
    $arrHoliday = [];
    foreach ($holidays as $holiday) {
        $arrHoliday[] = Carbon::parse($holiday)->format('Y-m-d');
    }

    return $start->diffInDaysFiltered(function (Carbon $date) use ($arrHoliday) {
        return $date->isWeekday() && ! in_array($date->format('Y-m-d'), $arrHoliday);
    }, $end);
}

function formatDate($date, $formatOutpout) {
    return Carbon::parse($date)->format($formatOutpout);
}

function formatDateFromExcelValue($value)
{
    if (is_numeric($value)) {
        return Carbon::instance(Date::excelToDateTimeObject($value))->format('Y-m-d');
    } else {
        return formatDate($value, 'Y-m-d');
    }
}