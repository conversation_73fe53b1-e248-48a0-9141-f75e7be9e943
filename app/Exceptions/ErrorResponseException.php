<?php

namespace App\Exceptions;

use App\Enums\ECommon;
use App\Services\Service;
use Exception;

class ErrorResponseException extends Exception
{
    /**
     * Constructor ErrorResponseException.
     *
     * @param string $message
     * @param $status
     * @param $errors
     */
    public function __construct(string $message, $status, $errors)
    {
        parent::__construct($message, $status);
        abort(Service::response()->error(ECommon::RESPONSE_CODE_FAILURE, $message, $status, $errors));
    }
}
