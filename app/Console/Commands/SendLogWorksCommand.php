<?php

namespace App\Console\Commands;

use App\Jobs\SendTeamsLogWorkNoticeJob;
use App\Services\ProjectService;
use Illuminate\Console\Command;

use App\Traits\ApiResponse;

class SendLogWorksCommand extends Command
{
    use ApiResponse;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:log_works {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send log works to teams';

    protected $projectService;

    public function __construct(ProjectService $projectService)
    {
        parent::__construct();
        $this->projectService = $projectService;
    }
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = $this->argument('date') ?? date('Y-m-d');
        $projects = $this->projectService->getProjectLogWork($date);
        foreach ($projects as $project) {
            if ($project->users) {
                $content = [
                    'text' => "<strong>Danh sách nhân viên chưa log work đủ ngày " . date('d-m-Y', strtotime($date)) . ":</strong>",
                ];
                foreach ($project->users as $user) {
                    $content['text'] .= "<p>- <b>{$user['name']}</b> ({$user['email']}) - Allocation: {$user['allocation']}h - Log work: {$user['log_work']}h</p>";
                }
                SendTeamsLogWorkNoticeJob::dispatch($content, $project->teams_webhook_url);
            }
        }
        $this->info('Done!');
    }
}
