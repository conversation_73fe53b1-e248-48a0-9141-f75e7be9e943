<?php

namespace App\Console\Commands;

use App\Enums\ETask;
use Illuminate\Console\Command;

use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;
use App\Jobs\UpdateTasksJob;
use App\Utils\Backlog;
use DateTime;

class UpdateTasksCommand extends Command
{
    use ProjectServiceSupport, UserServiceSupport;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:tasks {project_ids?} {type?} {updated_since?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize data tasks from backlog';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $projectIds = $this->argument('project_ids') ? explode(",", $this->argument('project_ids')) : [];
        $updatedSince = $this->argument('updated_since');
        $backlog = new Backlog(env('BACKLOG_SPACE_ID'), env('BACKLOG_API_KEY'));
        $perPage = 100; // max=100
        $offset = 0;
        $projects = $this->__getListProjects([
            'project_ids' => $projectIds,
            'fields' => ['id', 'backlog_project_id'],
            'disable_paginate' => 1,
        ]);
        $projects = collect($projects)->whereNotNull('backlog_project_id')->pluck('id', 'backlog_project_id')->toArray();
        if ($projects) {
            $users = $this->__getUsers(['fields' => ['user_id', 'email']]);
            $users = collect($users)->pluck('user_id', 'email')->toArray();
            do {
                $params = [
                    'count' => $perPage,
                    'offset' => $offset,
                    'projectId' => array_keys($projects),
                ];
                if ($this->argument('type') == ETask::CREATE_TASK) {
                    $params['createdSince'] = $updatedSince ?: env('ISSUES_CREATED_SINCE');
                } else {
                    $params['updatedSince'] = $updatedSince ?: date('Y-m-d');
                }
                $issues = $backlog->issues->get($params);
                $issues = array_map(function($issue) use ($projects, $users) {
                    $billableEffort = array_values(array_filter(@$issue['customFields'] ?: [], function($item) {
                        return $item['name'] == 'Billable Effort';
                    }));

                    return [
                        'project_id' => $projects[$issue['projectId']],
                        'backlog_project_id' => $issue['projectId'],
                        'issue_id' => $issue['id'],
                        'user_id' => @$users[$issue['assignee']['mailAddress']],
                        'title' => $issue['summary'],
                        'issue_type' => $issue['issueType']['name'],
                        'status' => $issue['status']['name'],
                        'start_date' => $issue['startDate'] ? (new DateTime($issue['startDate']))->format('Y-m-d') : null,
                        'due_date' => $issue['dueDate'] ? (new DateTime($issue['dueDate']))->format('Y-m-d') : null,
                        'estimate_hours' => $issue['estimatedHours'],
                        'actual_hours' => $issue['actualHours'],
                        'billable' => @$billableEffort[0]['value'] ?: 0,
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'updated_at' => now()->format('Y-m-d H:i:s'),
                    ];
                }, $issues);
                UpdateTasksJob::dispatch($issues);
                $offset += $perPage;
            } while (count($issues) === $perPage);
        }

        $this->info('Update tasks done');
    }
}
