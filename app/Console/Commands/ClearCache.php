<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear cache description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $cache = [
            'holidays',
        ];

        try {
            foreach ($cache as $key) {
                $this->info($key);
                var_dump(Cache::get($key));
                Cache::delete($key);
            }
        } catch (\Exception $e) {
            $this->info($e->getMessage());
            var_dump($e->getTraceAsString());
        }
    }
}
