<?php

namespace App\Console\Commands;

use App\Enums\ENoti;
use App\Enums\ERole;
use App\Jobs\NotiJob;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;
use Illuminate\Console\Command;

class TestNoti extends Command
{
    use ProjectServiceSupport;
    use UserServiceSupport;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:noti';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $project_id = 149;
            $user_id = 907;
            $message = 'test noti 1';
            $sender_avatar = '';
            $type = ENoti::PROJECTS_TYPE;

            $user = $this->__getUsers(['user_ids' => [$user_id]]);
            var_dump($user[0]->avatar);

            $project = $this->__getProject($project_id);
            $user_ids = array_column($project->user_role->data, 'user_id');
            var_dump(implode(', ', $user_ids));

            $pms = $this->__getMemberIdsByRoles($project_id, [ERole::PM]);
            var_dump(implode(', ', $pms ?? []));

            //dispatch(new NotiJob($project_id, $user_id, $message, $sender_avatar, $type))->delay(now()->addSecond());

            $this->info('done');
        } catch (\Exception $e) {
            var_dump($e->getMessage());
            var_dump($e->getTraceAsString());
        }
    }
}
