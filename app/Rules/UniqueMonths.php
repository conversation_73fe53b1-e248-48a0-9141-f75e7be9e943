<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class UniqueMonths implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $months = collect($value)->pluck('month');
        return $months->count() === $months->unique()->count();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('validation.resource_rental_cost.unique_months');
    }
}
