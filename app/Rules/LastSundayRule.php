<?php

namespace App\Rules;

use Carbon\Carbon;
use Illuminate\Contracts\Validation\Rule;

class LastSundayRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $endOfLastWeek = Carbon::now()->subWeek(2)->startOfWeek();
        $reportDate = Carbon::createFromFormat('Y-m-d', $value);

        return $reportDate->gt($endOfLastWeek);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('date_less_than_now');
    }
}
