<?php

namespace App\Http\Controllers;

use App\Models\Permission;
use App\Models\PermissionRole;
use App\Models\Role;
use App\Traits\HttpClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class AdminPermissionController extends Controller
{
    use HttpClient;

    const POSITION_TYPE = 1;

    const ROLE_TYPE = 2;

    const EXCLUDE_IDS = [67];

    const INCLUDE_POSITIONS = [
        'Chairman',
        'CEO',
        'COO',
        'CTO',
        'Kế Toán',
        'Kế Toán Trưởng',
        'Division Leader',
        'Vice Division Leader',
        'Amoeba Leader',
        'PQA Leader',
        'PQA',
        'HRM',
        'Sales',
        'Sales Manager',
        'PM',
        'SA',
        'Business Development',
        'C&B',
    ];

    /**
     * AdminPermissionController constructor.
     */
    public function __construct()
    {
        $this->middleware(['admin.permission']);
    }

    public function positions(Request $request)
    {
        $permissions = Permission::select(['*'])
            ->where(function ($query) {
                return $query->where('type', self::POSITION_TYPE)
                    ->orWhere('type', self::ROLE_TYPE);
            })
            ->whereNotIn('id', self::EXCLUDE_IDS)
            ->orderBy('order', 'asc')->get()->toArray();
        $permissions = $this->_group_by($permissions, 'group');

        $positions = Http::get(env('LINK_USER_SERVICE') . 'position')->json()['data'];
        $positions = array_filter($positions, function ($item) {
            return in_array($item['name'], self::INCLUDE_POSITIONS);
        });

        $routes = PermissionRole::whereNotNull('position_id')->get()->toArray();
        $routes = $this->_group_by($routes, 'position_id');
        $routes = array_map(function ($route) {
            return array_column($route, 'permission_id');
        }, $routes);

        return view('positions', [
            'positions' => $positions,
            'permissions' => $permissions,
            'routes' => $routes,
        ]);
    }

    public function _group_by($array, $key)
    {
        $return = [];
        foreach ($array as $val) {
            $return[$val[$key]][] = $val;
        }

        return $return;
    }

    public function storePositions(Request $request)
    {
        $routes = $request->input('routes');
        if (empty($routes)) {
            return back();
        }

        try {
            // post position permission
            $this->httpPost(env('LINK_PROJECT_SERVICE') . 'permission/positions', [
                'routes' => $routes,
            ]);

            return redirect()->back()->with('success', 'Save positions success');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function roles(Request $request)
    {
        $roles = Role::all()->load('permissions')->toArray();
        $permissions = Permission::whereType(self::ROLE_TYPE)->orderBy('order', 'asc')->get()->toArray();
        $permissions = $this->_group_by($permissions, 'group');

        return view('roles', [
            'roles' => $roles,
            'permissions' => $permissions,
        ]);
    }

    public function storeRoles(Request $request)
    {
        $routes = $request->input('routes');
        if (empty($routes)) {
            return back();
        }

        try {
            // post role permission
            $this->httpPost(env('LINK_PROJECT_SERVICE') . 'permission/roles', [
                'routes' => $routes,
            ]);

            return redirect()->back()->with('success', 'Save roles success');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
}
