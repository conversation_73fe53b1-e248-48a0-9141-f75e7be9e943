<?php

namespace App\Http\Controllers;

use App\Http\Requests\StaffCoefficientRequest;
use App\Http\Requests\UpdateStaffCoefficientRequest;
use App\Services\StaffCoefficientService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class StaffCoefficientController extends Controller
{
    private $staffCoefficicentService;

    public function __construct(StaffCoefficientService $staffCoefficicentService)
    {
        $this->staffCoefficicentService = $staffCoefficicentService;
    }

    public function index(Request $request)
    {
        try {
            $staffCoefficicents = $this->staffCoefficicentService->getListStaffCoefficient($request->all());

            return $this->sendSuccess(['staff_coefficients' => $staffCoefficicents]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function store(StaffCoefficientRequest $request)
    {
        try {
            $staffCoefficicentResponse = $this->staffCoefficicentService->store($request->all());

            if (@$staffCoefficicentResponse['code'] === Response::HTTP_UNPROCESSABLE_ENTITY) {
                return $this->sendValidate($staffCoefficicentResponse['message']);
            }

            return $this->sendCreated(['success' => true]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function update(UpdateStaffCoefficientRequest $request, $id)
    {
        try {
            $staffCoefficicentResponse = $this->staffCoefficicentService->update($request->all(), $id);

            if (@$staffCoefficicentResponse['code'] === Response::HTTP_UNPROCESSABLE_ENTITY) {
                return $this->sendValidate($staffCoefficicentResponse['message']);
            }

            return $this->sendUpdated(['success' => true]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function destroy($id)
    {
        try {
            $result = $this->staffCoefficicentService->delete($id);

            return $this->sendDeleted();
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
