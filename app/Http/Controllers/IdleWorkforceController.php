<?php

namespace App\Http\Controllers;

use App\Services\IdleWorkforceService;
use App\Services\UserService;
use App\Traits\DivisionSupport;
use Illuminate\Http\Request;

class IdleWorkforceController extends Controller
{
    use DivisionSupport;
    private $idleWorkforceService;

    private $userService;

    public function __construct(IdleWorkforceService $idleWorkforceService, UserService $userService)
    {
        $this->idleWorkforceService = $idleWorkforceService;
        $this->userService = $userService;
    }

    public function index(Request $request)
    {
        $args = $request->only([
            'from', 'to', 'free_from', 'free_to', 'name', 'division_ids', 'team_ids',
            'skill_ids', 'level_ids', 'position_ids', 'page', 'limit', 'view_module'
        ]);
        $divisions = $this->userService->getDivisions(['is_active_project' => true]);
        $divisionIds = array_column($divisions, 'id');
        $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;

        $positions = $this->userService->__getPositions(['is_active_project' => true]);
        $positionIds = array_column($positions, 'id');
        $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;

        $idleWorkforce = $this->idleWorkforceService->getIdleWorkforce($args);

        return $this->sendSuccess(['idle_workforce' => $idleWorkforce]);
    }

    public function getPositionAndSkillStatistic(Request $request)
    {
        try {
            $args = $request->all();
            $positionSkillStats = $this->__getIdleWorkforcePositionSkillStats($args);

            return $this->sendSuccess(['data' => $positionSkillStats]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getDivisionStatistic(Request $request)
    {
        try {
            $args = $request->all();
            $reportStatistic = $this->idleWorkforceService->__getIdleWorkforceDivisionStats($args);

            return $this->sendSuccess(['data' => $reportStatistic]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getMonthStatistic(Request $request)
    {
        try {
            $args = $request->all();
            $reportStatistic = $this->idleWorkforceService->__getIdleWorkforceMonthStats($args);

            return $this->sendSuccess(['data' => $reportStatistic]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getAvailableEmployee(Request $request)
    {
        try {
            $args = $request->all();

            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
    
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            
            $availableEmployees = $this->__getAvailableEmployee($args);

            return $this->sendSuccess(['available_employees' => $availableEmployees]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
