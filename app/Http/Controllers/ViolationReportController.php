<?php

namespace App\Http\Controllers;

use App\Enums\EActionLog;
use App\Http\Requests\ViolationReportRequest;
use App\Services\ViolationReportService;
use Illuminate\Http\Request;

class ViolationReportController extends Controller
{
    private $violationReportService;

    public function __construct(ViolationReportService $violationReportService)
    {
        $this->violationReportService = $violationReportService;
    }

    public function index(Request $request)
    {
        try {
            $result = $this->violationReportService->getListViolationReport($request->all());

            return $this->sendSuccess(['violation_reports' => $result]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function store(ViolationReportRequest $request)
    {
        try {
            $result = $this->violationReportService->store($request->all());

            return $this->sendCreated(['violation_report' => $result]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function update(ViolationReportRequest $request, $id)
    {
        try {
            $result = $this->violationReportService->update($request->all(), $id);
            if (! $result) return $this->sendNotFound();

            return $this->sendUpdated(['violation_report' => $result]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            $result = $this->violationReportService->__deleteViolationReport($request->all(), $id);
            if (! $result) return $this->sendNotFound();

            return $this->sendDeleted(__('delete_data_success'));
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getLogActivities(Request $request)
    {
        try {
            $args = $request->all();
            $args['events'] = EActionLog::VIOLATION_REPORT_EVENTS;
            $activityLogs = $this->violationReportService->__getViolationReportActivityLogs($args);

            return $this->sendSuccess(['activity_logs' => $activityLogs]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
