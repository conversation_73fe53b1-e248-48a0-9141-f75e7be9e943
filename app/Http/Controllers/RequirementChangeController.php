<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateRequirementChange;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\UpdateRequirementChange;
use App\Services\RequirementChangeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class RequirementChangeController extends Controller
{
    private RequirementChangeService $requirementChangeService;

    public function __construct(RequirementChangeService $requirementChangeService)
    {
        $this->requirementChangeService = $requirementChangeService;
    }

    public function index(Request $request, $projectId)
    {
        try {
            $args = $request->all();
            $data = $this->requirementChangeService->getListRequirementChange($args, $projectId);

            return $this->sendSuccess(['requirement_changes' => $data]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function store(CreateRequirementChange $request, $projectId)
    {
        try {
            $args = $request->all();
            $data = $this->requirementChangeService->store($args, $projectId);

            return $this->sendCreated(['requirement_change' => $data]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function update(UpdateRequirementChange $request, $projectId, $requirementChangeId)
    {
        try {
            $args = $request->all();
            $data = $this->requirementChangeService->update($args, $projectId, $requirementChangeId);
            if (!$data) {
                return $this->sendNotFound();
            }

            return $this->sendUpdated(['requirement_change' => $data]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function destroy($projectId, Request $request)
    {
        try {
            $args = $request->all();
            $result = $this->requirementChangeService->destroy($projectId, $args);

            if (!$result) {
                return $this->sendNotFound();
            }

            return $this->sendDeleted();
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function show($projectId, $stageId)
    {
        try {
            $data = $this->requirementChangeService->show($projectId, $stageId);

            if (!$data) {
                return $this->sendNotFound();
            }

            return $this->sendSuccess(['requirement_changes' => $data]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function import(ImportRequest $request, $projectId)
    {
        try {
            $dataImport = $this->requirementChangeService->getDataImport($request->file, $projectId);
            if (isset($dataImport['errors'])) {
                return response()->json([
                    'status' => ResponseAlias::HTTP_UNPROCESSABLE_ENTITY,
                    'messages' => $dataImport['errors'],
                ], ResponseAlias::HTTP_UNPROCESSABLE_ENTITY);
            } else {
                $success = $this->requirementChangeService->import($dataImport['data_import'], $projectId);
                if (is_int($success)) {
                    return $this->sendSuccess(['success' => $success]);
                }

                $success = json_decode(json_encode($success), true);
                if ($success['status'] == JsonResponse::HTTP_OK) {
                    return $this->sendSuccess(['success' => $success]);
                }

                return response()->json([
                    'status' => JsonResponse::HTTP_BAD_REQUEST,
                    'messages' => $success['messages'],
                ]);
            }
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function downloadTemplateImport(Request $request)
    {
        try {
            $link = $request->link;

            return response()->streamDownload(function () use ($link) {
                echo file_get_contents($link);
            }, basename($link));
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
