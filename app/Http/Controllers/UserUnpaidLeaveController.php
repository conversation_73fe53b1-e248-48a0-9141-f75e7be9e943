<?php

namespace App\Http\Controllers;

use App\Http\Requests\UsersUnpaidLeaveRequest;
use App\Services\UserUnpaidLeaveService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UserUnpaidLeaveController extends Controller
{
    private $userUnpaidLeaveService;

    public function __construct(UserUnpaidLeaveService $userUnpaidLeaveService)
    {
        $this->userUnpaidLeaveService = $userUnpaidLeaveService;
    }

    public function index(Request $request)
    {
        try {
            $usersUnpaidLeave = $this->userUnpaidLeaveService->getListUserUnpaidLeave($request->all());

            return $this->sendSuccess(['users_unpaid_leave' => $usersUnpaidLeave]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function store(UsersUnpaidLeaveRequest $request)
    {
        try {
            $userUnpaidLeave = $this->userUnpaidLeaveService->store($request->all());

            if (@$userUnpaidLeave->code == Response::HTTP_UNPROCESSABLE_ENTITY) {
                return $this->sendValidate($userUnpaidLeave->message);
            }

            return $this->sendCreated(['users_unpaid_leave' => $userUnpaidLeave]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function update(UsersUnpaidLeaveRequest $request, $id)
    {
        try {
            $userUnpaidLeave = $this->userUnpaidLeaveService->update($request->all(), $id);

            if (@$userUnpaidLeave->code == Response::HTTP_UNPROCESSABLE_ENTITY) {
                return $this->sendValidate($userUnpaidLeave->message);
            }

            if (! $userUnpaidLeave) {
                return $this->sendNotFound();
            }

            return $this->sendUpdated(['users_unpaid_leave' => $userUnpaidLeave]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function destroy($id)
    {
        try {
            $result = $this->userUnpaidLeaveService->destroy($id);

            if (! $result) {
                return $this->sendNotFound();
            }

            return $this->sendDeleted();
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
