<?php

namespace App\Http\Controllers;

use App\Http\Requests\RentalCostsRequest;
use App\Services\ResourceRentalCostService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ResourceRentalCostController extends Controller
{
    private ResourceRentalCostService $resourceRentalCostService;

    public function __construct(ResourceRentalCostService $resourceRentalCostService)
    {
        $this->resourceRentalCostService = $resourceRentalCostService;
    }

    public function index(Request $request, $projectId)
    {
        try {
            $args = $request->all();
            $args['project_ids'] = $projectId;

            $resourceRentalCosts = $this->resourceRentalCostService->getList($args);

            return $this->sendSuccess(['resource_rental_costs' => $resourceRentalCosts]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function store(RentalCostsRequest $request, $projectId)
    {
        try {
            $args = $request->all();
            $args['project_id'] = $projectId;

            $resourceRentalCosts = $this->resourceRentalCostService->updateOrCreate($args);

            if (@$resourceRentalCosts->code == Response::HTTP_UNPROCESSABLE_ENTITY) {
                return $this->sendValidate($resourceRentalCosts->message);
            }

            return $this->sendCreated(['resource_rental_costs' => $resourceRentalCosts->data]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function destroy($projectId, $id)
    {
        try {
            $result = $this->resourceRentalCostService->destroy($id);

            if (!$result) {
                return $this->sendNotFound();
            }

            return $this->sendDeleted();
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
