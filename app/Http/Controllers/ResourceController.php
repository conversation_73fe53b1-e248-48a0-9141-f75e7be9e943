<?php

namespace App\Http\Controllers;

use App\Enums\EAllocation;
use App\Enums\EBase;
use App\Enums\EContractType;
use App\Exports\AllocationsExport;
use App\Services\AllocationService;
use App\Services\DailyReportService;
use App\Services\ProjectService;
use App\Services\ResourceService;
use App\Services\UserService;
use App\Services\UserUnpaidLeaveService;
use App\Traits\ExtraFunctions;
use App\Utils\PaginateCollection;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Maatwebsite\Excel\Facades\Excel;
use App\Enums\EDailyReport;
use App\Enums\EProject;
use App\Enums\ERole;
use App\Exports\LogWorkExport;
use App\Traits\Helper;
use Carbon\CarbonPeriod;
use App\Enums\EProjectType;

class ResourceController extends Controller
{
    use ExtraFunctions, Helper;

    private $resourceService;

    private $allocationService;

    private $projectService;

    private $userService;

    private $userUnpaidLeaveService;

    private $dailyReportService;

    public function __construct(
        ResourceService $resourceService,
        AllocationService $allocationService,
        ProjectService $projectService,
        UserService $userService,
        UserUnpaidLeaveService $userUnpaidLeaveService,
        DailyReportService $dailyReportService
    ) {
        $this->resourceService = $resourceService;
        $this->allocationService = $allocationService;
        $this->projectService = $projectService;
        $this->userService = $userService;
        $this->userUnpaidLeaveService = $userUnpaidLeaveService;
        $this->dailyReportService = $dailyReportService;
    }

    public function index(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'effort_from',
                'effort_to',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type',
                'contract_types',
                'team_ids',
                'only_allocate'
            ]);
            $args['pagination'] = false;
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;

            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;

            $teams = $this->userService->getTeams(['is_active_project' => true]);
            $teamIds = array_column($teams, 'id');
            $args['team_ids'] = isset($args['team_ids']) ? $args['team_ids'] : $teamIds;

            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            if (isset($args['from']) && isset($args['to'])) {
                $args['from'] = Carbon::createFromFormat('Y-m-d', $args['from'])->format('Y-m-d');
                $args['to'] = Carbon::createFromFormat('Y-m-d', $args['to'])->format('Y-m-d');
            } else {
                $args['from'] = Carbon::now()->startOfMonth()->format('Y-m-d');
                $args['to'] = Carbon::now()->endOfMonth()->format('Y-m-d');
            }

            $v2ApplyDate = config('app.v2_apply_date');
            if (isset($args['only_allocate']) && $args['only_allocate'] == 'true') {
                $v2ApplyDate = Carbon::parse($args['to'])->addMonths(1)->format('Y-m-d');
            }

            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            /**
             * nếu filter theo mã dữ án hoặc loại dự án, chỉ lấy user được add vào dự án
             */
            if (isset($args['project_type'])) {
                $projects = $this->projectService->getProjects([
                    'disable_paginate' => 1,
                    'project_type' => $args['project_type']
                ]);
                $args['project_ids'] = array_column($projects, 'id');
            }

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }

            $resources = $this->resourceService->getResources(
                array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']])
            );
            $resources = collect($resources);
            $tmpResources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetAllocation = [
                'user_ids' => $args['user_ids'] ?? null,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => EDailyReport::SUCCESS_STATUS,
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $paramsGetDaysLeave = [
                'user_ids' => $args['user_ids'] ?? null,
                'start_date' => $args['from'],
                'end_date' => Carbon::today()->format('Y-m-d'),
                'columns' => ['user_id', 'created_at', 'paid_leave']
            ];
            $daysLeave = $this->resourceService->__getDayLeave($paramsGetDaysLeave);
            $daysLeave = collect($daysLeave);

            $tmpResources->transform(function ($resource) use ($dailyReports, $allocations, $userUnpaidLeave, $daysLeave) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->reports_of_month = $reports->groupBy(fn($item) => Carbon::parse($item->work_date)->format('Y-m'))->toArray();
                $resource->reports_of_week = $reports->groupBy(fn($item) => Carbon::parse($item->work_date)->weekOfYear)->toArray();
                $resource->daily_reports = $reports;
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $projectsName = $allocates
                    ->filter(fn($allocate) => $allocate->user_id == $resource->user_id)
                    ->map(function ($allocate) {
                        if (isset($allocate->project)) return $allocate->project;
                    })
                    ->unique()
                    ->reduce(fn($prev, $item) => $item->name ?? $item . ', ' . $prev, '');
                $resource->project = rtrim($projectsName, ', ');
                $resource->allocates = $allocates->values()->toArray();
                $unpaidLeave = $userUnpaidLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->unpaid_leave = $unpaidLeave->values()->toArray();
                $resource->days_leave = $daysLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                return $resource;
            });

            $tmpResources = $this->resourceService->mapResourceEffort($tmpResources, $holiday, $v2ApplyDate, $args['from'], $args['to']);

            if (isset($args['effort_from']) && isset($args['effort_to'])) {
                $tmpResources = $this->calEffort($tmpResources, $args['effort_from'], $args['effort_to'], $args['from'], $args['to']);
            }
            $tmpResources = $this->mapUnpaidLeaveToResource($tmpResources, $userUnpaidLeave);

            $resources = PaginateCollection::paginate($tmpResources, $args['limit'] ?? Config::get('define.resourcePagination'));

            return $this->sendSuccess(['resources' => $resources]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    private function mapUnpaidLeaveToResource($resources, $userUnPaidLeaves)
    {
        $userIds = $resources->pluck('user_id')->toArray();

        $userUnPaidLeaves = $userUnPaidLeaves
            ->groupBy('user_id')
            ->map(function ($item) {
                return collect($item)->map(function ($item) {
                    return [
                        'start_date' => $item->start_date,
                        'end_date' => $item->end_date
                    ];
                });
            })
            ->toArray();

        if ($userUnPaidLeaves) {
            $resources->each(function ($item) use ($userUnPaidLeaves) {
                $item->unpaid_leaves = $userUnPaidLeaves[$item->user_id] ?? [];
            });
        }
        return $resources;
    }

    private function calEffort($resources, $effortFrom, $effortTo, $startDate, $endDate)
    {
        $startDate = Carbon::createFromFormat('Y-m-d', $startDate);
        $endDate = Carbon::createFromFormat('Y-m-d', $endDate);
        $totalDays = $startDate->diffInDaysFiltered(function (Carbon $date) {
            return $date->isWeekday();
        }, $endDate) + 1;

        $resources = $resources->map(function ($user) use ($startDate, $endDate, $totalDays) {
            $user->effort = 0;
            $user->effort_empty = 100;
            if (isset($user->day_schedule)) {
                $effort = collect($user->day_schedule)
                    ->map(function ($daySchedule) use ($startDate, $endDate) {
                        $checkDateBetween = Carbon::createFromFormat('Y-m-d', $daySchedule['work_date'])->between($startDate, $endDate);
                        if ($checkDateBetween) return $daySchedule;
                    })
                    ->filter()
                    ->reduce(fn($prev, $item) => $prev + $item['actual_effort'], 0);
                $user->effort = $effort / $totalDays;
                $user->effort_empty = 100 - $user->effort;
            }
            return $user;
        })->filter(
            fn($user) => ($user->effort >= $effortFrom && $user->effort <= $effortTo)
        )->values();

        return $resources;
    }

    public function getManMonthPosition(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $date = Carbon::createFromDate($request->date ?? date('Y-m-d'));
            $onlyAllocate = $request->only_allocate ?? "false";
            $currentMonth = Carbon::now()->format('Y-m');
            $firstDateOfMonth = $date->firstOfMonth()->format('Y-m-d');
            $lastDateOfMonth = $date->lastOfMonth()->format('Y-m-d');
            $args = $request->only(['date']);
            $args['from'] = $firstDateOfMonth;
            $args['to'] = $lastDateOfMonth;
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            $requestMonth = $request->date;

            $allResources = $this->resourceService->getResources(array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']]));
            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeaveCollection = collect($userUnpaidLeave);
            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave(collect($allResources), $userUnpaidLeaveCollection, $args);

            $v2ApplyDate = config('app.v2_apply_date');
            // calculate busy rate by allocations in cases:
            //    only_allocate === "true"
            // OR month in future
            // OR month before apply date
            if ($onlyAllocate === "true" || $requestMonth > $currentMonth || $requestMonth <= Carbon::createFromFormat('Y-m-d', $v2ApplyDate)->subMonth()->format('Y-m')) {
                $allocations = $this->allocationService->getAllocationInMonth($args);
                $result = $this->calBusyRateWithAllocationByChartType(
                    'position',
                    $positions,
                    $args,
                    $resources,
                    $allocations,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            } elseif ($requestMonth < $currentMonth) {
                // past month
                $dailyReports = $this->dailyReportService->getReports([
                    'from_date' => $args['from'],
                    'to_date' => $args['to'],
                    'status' => [EDailyReport::SUCCESS_STATUS],
                ]);
                $result = $this->calBusyRateWithReportByChartType(
                    'position',
                    $positions,
                    $args,
                    $resources,
                    $dailyReports,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            } else {
                // current month
                $allocations = $this->allocationService->getAllocationInMonth(
                    array_merge($args, [
                        'from' => Carbon::now()->format('Y-m-d'),
                        'to' => $lastDateOfMonth
                    ])
                );
                $dailyReports = $this->dailyReportService->getReports([
                    'from_date' => $firstDateOfMonth,
                    'to_date' => Carbon::yesterday()->format('Y-m-d'),
                    'status' => [EDailyReport::SUCCESS_STATUS]
                ]);
                $result = $this->calBusyRateCurrentMonthByChartType(
                    'position',
                    $positions,
                    $args,
                    $resources,
                    $allocations,
                    $dailyReports,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            }

            return $this->sendSuccess(['positions' => $result]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    private function countMembersByChartType($resources, $chartType)
    {
        switch ($chartType) {
            case 'position':
                $countByChartType = $resources
                    ->groupBy('position.name')
                    ->map(fn($members) => $members->count());
                break;
            case 'division':
                $countByChartType = $resources
                    ->groupBy('division.name')
                    ->map(fn($members) => $members->count());
                break;
            case 'skill':
                $countByChartType = $resources
                    ->map(function ($user) {
                        $userWithSkillName = clone $user;
                        $userWithSkillName->skills = array_column($user->skills, 'skill');
                        return $userWithSkillName;
                    })
                    ->groupBy('skills')
                    ->map(fn($members) => $members->count());
                break;
            default:
                $countByChartType = [];
        }
        return $countByChartType;
    }

    private function calBusyRateWithAllocationByChartType($chartType, $chartItems, $args, $resources, $allocations, $holiday, $userUnpaidLeave)
    {
        $allocationsWithUnpaidLeave = $this->resourceService->mapAllocateUnpaidLeave($resources, $allocations, $userUnpaidLeave, $holiday);
        $resourcesByChartType = $this->resourceService->mapResourceAllocationByChartType(
            $chartType,
            $resources,
            $allocationsWithUnpaidLeave,
            $args['from'],
            $args['to'],
            $holiday,
            $userUnpaidLeave
        );
        return $this->mapResourceCountByItem(
            $chartItems,
            $resourcesByChartType,
            $this->countMembersByChartType($resources, $chartType)
        );
    }

    private function calBusyRateWithReportByChartType($chartType, $chartItems, $args, $resources, $dailyReports, $holiday, $userUnpaidLeave)
    {
        $resourcesByChartType = $this->resourceService->mapResourceDailyReportByChartType(
            $chartType,
            $resources,
            $dailyReports,
            $args['from'],
            $args['to'],
            $holiday,
            $userUnpaidLeave
        );
        return $this->mapResourceCountByItem(
            $chartItems,
            $resourcesByChartType,
            $this->countMembersByChartType($resources, $chartType)
        );
    }

    private function calBusyRateCurrentMonthByChartType($chartType, $chartItems, $args, $resources, $allocations, $dailyReports, $holiday, $userUnpaidLeave)
    {
        $allocationsWithUnpaidLeave = $this->resourceService->mapAllocateUnpaidLeave($resources, $allocations, $userUnpaidLeave, $holiday);
        $resourcesByChartType = $this->resourceService->mapResourceDailyReportAllocationByChartType(
            $chartType,
            $resources,
            $dailyReports,
            $allocations,
            $args['from'],
            $args['to'],
            $holiday,
            $userUnpaidLeave
        );
        return $this->mapResourceCountByItem(
            $chartItems,
            $resourcesByChartType,
            $this->countMembersByChartType($resources, $chartType)
        );
    }

    private function mapResourceCountByItem($items, $resources, $memberCountByItemName)
    {
        $itemByNameCollection = collect($items)->keyBy('name');
        $resourceByItem = $itemByNameCollection
            ->map(fn() => ['free' => 0, 'allocated' => 0, 'overload' => 0])
            ->merge($resources)
            ->map(function ($resourceCount, $itemName) use ($memberCountByItemName, $itemByNameCollection) {
                $resourceCount['total_member'] = $memberCountByItemName[$itemName] ?? 0;
                $resourceCount['id'] = $itemByNameCollection[$itemName]->id;
                return $resourceCount;
            });
        return $resourceByItem;
    }

    public function getManMonthSkill(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $date = Carbon::createFromDate($request->date ?? date('Y-m-d'));
            $onlyAllocate = $request->only_allocate ?? "false";
            $currentMonth = Carbon::now()->format('Y-m');
            $firstDateOfMonth = $date->firstOfMonth()->format('Y-m-d');
            $lastDateOfMonth = $date->lastOfMonth()->format('Y-m-d');
            $args = $request->only(['date']);
            $args['from'] = $firstDateOfMonth;
            $args['to'] = $lastDateOfMonth;
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            $skills = $this->userService->__getSkill();
            $skillIds = array_column($skills, 'id');
            $args['skill_ids'] = $skillIds;
            $requestMonth = $request->date;

            $allResources = $this->resourceService->getResources(array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']]));
            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeaveCollection = collect($userUnpaidLeave);
            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave(collect($allResources), $userUnpaidLeaveCollection, $args);

            $v2ApplyDate = config('app.v2_apply_date');
            // calculate busy rate by allocations in cases:
            //    only_allocate === "true"
            // OR month in future
            // OR month before apply date
            if ($onlyAllocate === "true" || $requestMonth > $currentMonth || $requestMonth <= Carbon::createFromFormat('Y-m-d', $v2ApplyDate)->subMonth()->format('Y-m')) {
                $allocations = $this->allocationService->getAllocationInMonth($args);
                $result = $this->calBusyRateWithAllocationByChartType(
                    'skill',
                    $skills,
                    $args,
                    $resources,
                    $allocations,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            } elseif ($requestMonth < $currentMonth) {
                // past month
                $dailyReports = $this->dailyReportService->getReports([
                    'from_date' => $args['from'],
                    'to_date' => $args['to'],
                    'status' => [EDailyReport::SUCCESS_STATUS],
                ]);
                $result = $this->calBusyRateWithReportByChartType(
                    'skill',
                    $skills,
                    $args,
                    $resources,
                    $dailyReports,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            } else {
                // current month
                $allocations = $this->allocationService->getAllocationInMonth(
                    array_merge($args, [
                        'from' => Carbon::now()->format('Y-m-d'),
                        'to' => $lastDateOfMonth
                    ])
                );
                $dailyReports = $this->dailyReportService->getReports([
                    'from_date' => $firstDateOfMonth,
                    'to_date' => Carbon::yesterday()->format('Y-m-d'),
                    'status' => [EDailyReport::SUCCESS_STATUS]
                ]);
                $result = $this->calBusyRateCurrentMonthByChartType(
                    'skill',
                    $skills,
                    $args,
                    $resources,
                    $allocations,
                    $dailyReports,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            }

            return $this->sendSuccess(['skills' => $result]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getBusyRateDivision(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $date = Carbon::createFromDate($request->date ?? date('Y-m-d'));
            $onlyAllocate = $request->only_allocate ?? "false";
            $currentMonth = Carbon::now()->format('Y-m');
            $firstDateOfMonth = $date->firstOfMonth()->format('Y-m-d');
            $lastDateOfMonth = $date->lastOfMonth()->format('Y-m-d');
            $args = $request->only(['date']);
            $args['from'] = $firstDateOfMonth;
            $args['to'] = $lastDateOfMonth;
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            $requestMonth = $request->date;

            $allResources = $this->resourceService->getResources(array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']]));
            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeaveCollection = collect($userUnpaidLeave);
            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave(collect($allResources), $userUnpaidLeaveCollection, $args);

            $v2ApplyDate = config('app.v2_apply_date');
            // calculate busy rate by allocations in cases:
            //    only_allocate === "true"
            // OR month in future
            // OR month before apply date
            if ($onlyAllocate === "true" || $requestMonth > $currentMonth || $requestMonth <= Carbon::createFromFormat('Y-m-d', $v2ApplyDate)->subMonth()->format('Y-m')) {
                $allocations = $this->allocationService->getAllocationInMonth($args);
                $result = $this->calBusyRateWithAllocationByChartType(
                    'division',
                    $divisions,
                    $args,
                    $resources,
                    $allocations,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            } elseif ($requestMonth < $currentMonth) {
                // past month
                $dailyReports = $this->dailyReportService->getReports([
                    'from_date' => $args['from'],
                    'to_date' => $args['to'],
                    'status' => [EDailyReport::SUCCESS_STATUS],
                ]);
                $result = $this->calBusyRateWithReportByChartType(
                    'division',
                    $divisions,
                    $args,
                    $resources,
                    $dailyReports,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            } else {
                // current month
                $allocations = $this->allocationService->getAllocationInMonth(
                    array_merge($args, [
                        'from' => Carbon::now()->format('Y-m-d'),
                        'to' => $lastDateOfMonth
                    ])
                );
                $dailyReports = $this->dailyReportService->getReports([
                    'from_date' => $firstDateOfMonth,
                    'to_date' => Carbon::yesterday()->format('Y-m-d'),
                    'status' => [EDailyReport::SUCCESS_STATUS]
                ]);
                $result = $this->calBusyRateCurrentMonthByChartType(
                    'division',
                    $divisions,
                    $args,
                    $resources,
                    $allocations,
                    $dailyReports,
                    $holiday,
                    $userUnpaidLeaveCollection
                );
            }
            return $this->sendSuccess(['divisions' => $result]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getBusyRateMonthV2(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->only(['year', 'only_allocate']);
            $date = Carbon::createFromDate($args['year'] ?? date('Y'));
            $args['from'] = $date->firstOfYear()->format('Y-m-d');
            $args['to'] = $date->lastOfYear()->format('Y-m-d');
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            $args['contract_types'] = EContractType::CONTRACT_TYPES_FOR_BUSY_RATE;

            $v2ApplyDate = config('app.v2_apply_date');
            if (isset($args['only_allocate']) && $args['only_allocate'] == 'true') {
                $v2ApplyDate = Carbon::parse($args['to'])->addMonths(1)->format('Y-m-d');
                $onlyAllocate = true;
            }

            $resources = $this->resourceService->getResources(array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']]));
            $resources = collect($resources);
            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            $paramsGetAllocation = [
                'user_ids' => $resources->pluck('user_id')->toArray(),
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS],
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0,
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $paramsGetDaysLeave = [
                'user_ids' => $args['user_ids'] ?? null,
                'start_date' => $args['from'],
                'end_date' => Carbon::today()->format('Y-m-d'),
                'columns' => ['user_id', 'created_at', 'paid_leave']
            ];
            $daysLeave = $this->resourceService->__getDayLeave($paramsGetDaysLeave);
            $daysLeave = collect($daysLeave);
            $resources->transform(function ($resource) use ($dailyReports, $allocations, $userUnpaidLeave, $daysLeave) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->daily_reports = $reports->groupBy(fn($item) => Carbon::parse($item->work_date)->format('Y-m'))->toArray();
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->allocates = $allocates->values()->toArray();
                $unpaidLeave = $userUnpaidLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->unpaid_leave = $unpaidLeave->values()->toArray();
                $daysLeave = $daysLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->days_leave = $daysLeave->values()->toArray();
                return $resource;
            });

            $months = $this->resourceService->getMonthsBetweenFormatYm($args['from'], $args['to']);
            $busyRateInMonth = $this->resourceService->calDivBusyRateInYear($resources, collect($months), $holiday, $v2ApplyDate, $onlyAllocate ?? false);
            return $this->sendSuccess(['busy_rate' => $busyRateInMonth]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getManMonthProjectType(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args['fields'] = ['id', 'project_type', 'billable', 'budget'];
            $allProjects = $this->projectService->getAllProject($args);

            $date = Carbon::createFromDate($request->date ?? date('Y-m-d'));
            $args['from'] = $date->firstOfMonth()->format('Y-m-d');
            $args['to'] = $date->lastOfMonth()->format('Y-m-d');

            $args['project_ids'] = array_column($allProjects, 'id');
            $allocations = $this->allocationService->getAllocationByProjectIds($args);

            $activeProjectIds = collect($allocations)
                ->map(fn($item) => $item->project->id)
                ->unique()->toArray();

            $projects = collect($allProjects)
                ->filter(function ($item) use ($activeProjectIds) {
                    return in_array($item->id, $activeProjectIds);
                })
                ->values();

            $projectType = $this->projectService->getProjectType();
            $projectTypeFlip = array_flip((array) $projectType);
            $projectType = collect(array_fill_keys(
                array_keys((array) $projectType),
                0
            ));

            $projectGrouped = collect($projects)
                ->groupBy('project_type')
                ->map(fn($project) => $project->count())
                ->flatMap(fn($type, $key) => [$projectTypeFlip[$key] => $type]);
            $projectGrouped = $projectType->merge($projectGrouped)->toArray();

            $projectResources = $this->resourceService->mapProjectResourceAllocation($allocations, $date->format('Y-m-d'), $holiday);
            $manMonthProjects = collect($projectResources)
                ->filter(fn($resource) => isset($resource->project))
                ->groupBy(fn($resource) => $resource->project->type)
                ->map(function ($groupProject) {
                    return $groupProject->reduce(
                        fn($prev, $project) => $prev + $project->allocatedOfMonth,
                        0
                    );
                })
                ->flatMap(fn($manMonth, $key) => [$projectTypeFlip[$key] => $manMonth]);

            $manMonthProjects = $projectType
                ->merge($manMonthProjects)
                ->map(function ($project, $key) use ($projectGrouped) {
                    $item['mm'] = $project;
                    $item['total'] = $projectGrouped[$key];

                    return $item;
                });

            return $this->sendSuccess(['project_type' => $manMonthProjects]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    /**
     * Version 2
     */
    public function getBusyRateV2(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $date = Carbon::createFromDate(date('Y-m-d'));
            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'effort_from',
                'effort_to',
                'team_ids',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type',
                'only_allocate',
                'contract_types'
            ]);
            $args['pagination'] = false;
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;

            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            if (isset($args['from']) && isset($args['to'])) {
                $args['from'] = Carbon::createFromFormat('Y-m-d', $args['from'])->format('Y-m-d');
                $args['to'] = Carbon::createFromFormat('Y-m-d', $args['to'])->format('Y-m-d');
            } else {
                $args['from'] = $date->firstOfMonth()->format('Y-m-d');
                $args['to'] = $date->lastOfMonth()->format('Y-m-d');
            }

            $v2ApplyDate = config('app.v2_apply_date');
            if (isset($args['only_allocate']) && $args['only_allocate'] == 'true') {
                $v2ApplyDate = Carbon::parse($args['to'])->addMonths(1)->format('Y-m-d');
                $onlyAllocate = true;
            }

            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            /**
             * nếu filter theo mã dữ án hoặc loại dự án, chỉ lấy user được add vào dự án
             */
            if (isset($args['project_type'])) {
                $projects = $this->projectService->getProjects([
                    'disable_paginate' => 1,
                    'project_type' => $args['project_type']
                ]);
                $args['project_ids'] = array_column($projects, 'id');
            }

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }

            if (@$args['contract_types']) {
                $args['contract_types'] = collect($args['contract_types'])
                    ->filter(fn($contractType) => in_array($contractType, EContractType::CONTRACT_TYPES_FOR_BUSY_RATE))
                    ->toArray();
            } else {
                $args['contract_types'] = EContractType::CONTRACT_TYPES_FOR_BUSY_RATE;
            }

            $resources = $this->resourceService->getResources(
                array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']])
            );
            $resources = collect($resources);
            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetAllocation = [
                'user_ids' => $args['user_ids'] ?? null,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'],
                'to_date' => $args['to'],
                'status' => [EDailyReport::SUCCESS_STATUS],
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $paramsGetDaysLeave = [
                'user_ids' => $args['user_ids'] ?? null,
                'start_date' => $args['from'],
                'end_date' => Carbon::today()->format('Y-m-d'),
                'columns' => ['user_id', 'created_at', 'paid_leave']
            ];
            $daysLeave = $this->resourceService->__getDayLeave($paramsGetDaysLeave);
            $daysLeave = collect($daysLeave)->map(function ($dayLeave) {
                $shifts = explode(',', $dayLeave->paid_leave);
                $leaveHours = 0;
                foreach ($shifts as $shift) {
                    if ($shift != 0) $leaveHours += 2;
                };
                $dayLeave->paid_leave = $leaveHours;
                return $dayLeave;
            });

            $resources->transform(function ($resource) use ($dailyReports, $allocations, $userUnpaidLeave, $daysLeave) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->daily_reports = $reports->values()->toArray();
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->allocates = $allocates->values()->toArray();
                $unpaidLeave = $userUnpaidLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->unpaid_leave = $unpaidLeave->values()->toArray();
                $resource->paid_leave = $daysLeave->filter(fn($item) => $item->user_id == $resource->user_id)
                    ->reduce(fn($prev, $item) => $prev + $item->paid_leave, 0);
                return $resource;
            });

            if (isset($args['effort_from']) && isset($args['effort_to'])) {
                $resources = $this->resourceService->mapResourceDayEffort($resources, $holiday, $v2ApplyDate, $args['from'], $args['to']);
                $resources = $this->calEffort($resources, $args['effort_from'], $args['effort_to'], $args['from'], $args['to']);
            }

            $busyRate = $this->resourceService->calBusyRate($resources, $args['from'], $args['to'], $holiday, $v2ApplyDate, $onlyAllocate ?? false);
            return $this->sendSuccess(['busy_rate' => $busyRate]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function exportResourceAllocation(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $date = Carbon::createFromDate(date('Y-m-d'));
            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'effort_from',
                'effort_to',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type',
                'contract_types',
                'team_ids',
            ]);
            $args['pagination'] = false;
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;

            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            if (isset($args['from']) && isset($args['to'])) {
                $args['from'] = Carbon::createFromFormat('Y-m-d', $args['from'])->format('Y-m-d');
                $args['to'] = Carbon::createFromFormat('Y-m-d', $args['to'])->format('Y-m-d');
            } else {
                $args['from'] = $date->firstOfMonth()->format('Y-m-d');
                $args['to'] = $date->lastOfMonth()->format('Y-m-d');
            }

            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            /**
             * nếu filter theo mã dữ án hoặc loại dự án, chỉ lấy user được add vào dự án
             */
            if (isset($args['project_type'])) {
                $projects = $this->projectService->getProjects([
                    'disable_paginate' => 1,
                    'project_type' => $args['project_type']
                ]);
                $args['project_ids'] = array_column($projects, 'id');
            }

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }

            $resources = $this->resourceService->getResources(
                array_merge($args, ['start_date' => $args['from'], 'end_date' => $args['to']])
            );

            $resources = collect($resources);
            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetAllocation = [
                'project_ids' => $args['project_ids'] ?? null,
                'user_ids' => $args['user_ids'] ?? null,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            if (isset($args['project_ids'])) $allocations = $this->allocationService->getAllocationByProjectIds($paramsGetAllocation);
            else $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);
            $users = $resources->keyBy('user_id')->toArray();
            $allocations = $this->projectService->mapCalenderEffort($allocations, $holiday, $args['from'], $args['to']);
            $allocations = $allocations
                ->filter(function ($allocation) use ($users) {
                    return array_key_exists($allocation->user_id, $users);
                })
                ->map(function ($allocation) use ($users, $args) {
                    $userId = $allocation->user_id;
                    $timeRangeStartDate = max($args['from'], $allocation->start_date);
                    $timeRangeEndDate = min($args['to'], $allocation->end_date);
                    return [
                        'user_code' => $users[$userId]->code ?? '',
                        'user_name' => $users[$userId]->name ?? '',
                        'user_email' => $users[$userId]->email ?? '',
                        'project_name' => $allocation->project->name ?? $allocation->project,
                        'division' => $users[$userId]->division->name ?? '',
                        'role_name' =>  $users[$userId]->position->name ?? '',
                        'level' =>  $users[$userId]->level->name ?? '',
                        'start_date' => Carbon::parse($timeRangeStartDate)->format('d/m/Y'),
                        'end_date' => Carbon::parse($timeRangeEndDate)->format('d/m/Y'),
                        'allocation' => $allocation->allocation,
                        'coefficient' => $allocation->coefficient,
                        "effort" => $allocation->effort,
                        "effort_with_coefficient" => $allocation->effort_with_coefficient,
                    ];
                })->toArray();
            return Excel::download(new AllocationsExport($allocations, EAllocation::COLUMNS_EXPORT),  'resource-allocation.xlsx');
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getDivisionEffort(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->only(['month', 'division_ids']);
            $date = Carbon::parse($args['month'] ?? Carbon::today());
            $args['from'] = $date->firstOfMonth()->format('Y-m-d');
            $args['to'] = $date->lastOfMonth()->format('Y-m-d');
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = $positionIds;
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            $resources = $this->resourceService->getResources(
                array_merge($args, [
                    'start_date' => $args['from'],
                    'end_date' => $args['to'],
                    'fields' => ['user_id', 'name', 'code', 'status', 'checkout_date', 'onboard_date', 'division', 'email']
                ])
            );
            $resources = collect($resources);
            $userIds = $resources->pluck('user_id')->toArray();
            $paramsGetAllocation = [
                'user_ids' => $userIds,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);
            $paramsGetReport = [
                'user_ids' => $userIds,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0,
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $resources->transform(function ($resource) use ($dailyReports, $allocations) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->approved_reports = $reports->filter(fn($item) => $item->status == EDailyReport::SUCCESS_STATUS)->values()->toArray();
                $resource->waiting_reports = $reports->filter(fn($item) => $item->status == EDailyReport::PENDING_STATUS)->values()->toArray();
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->allocates = $allocates->values()->toArray();
                return $resource;
            });
            $divisionEffort = $this->resourceService->mapDivisionEffort($resources, $args['from'], $args['to'], $holiday);

            return $this->sendSuccess(['division_effort' => $divisionEffort]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getTeamEffort(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->only(['month', 'division_ids']);
            $date = Carbon::parse($args['month'] ?? Carbon::today());
            $args['from'] = $date->firstOfMonth()->format('Y-m-d');
            $args['to'] = $date->lastOfMonth()->format('Y-m-d');
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = $positionIds;
            $teams = $this->userService->getTeams(['is_active_project' => true, 'division_id' => $args['division_ids'] ?? null]);

            $teamIds = array_column($teams, 'id');
            $args['team_ids'] = isset($args['team_ids']) ? $args['team_ids'] : $teamIds;
            $resources = $this->resourceService->getResources(
                array_merge($args, [
                    'start_date' => $args['from'],
                    'end_date' => $args['to'],
                    'fields' => ['user_id', 'name', 'code', 'status', 'checkout_date', 'onboard_date', 'division', 'email', 'team']
                ])
            );
            $resources = collect($resources);
            $userIds = $resources->pluck('user_id')->toArray();
            $paramsGetAllocation = [
                'user_ids' => $userIds,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);
            $paramsGetReport = [
                'user_ids' => $userIds,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0,
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $resources->transform(function ($resource) use ($dailyReports, $allocations) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->approved_reports = $reports->filter(fn($item) => $item->status == EDailyReport::SUCCESS_STATUS)->values()->toArray();
                $resource->waiting_reports = $reports->filter(fn($item) => $item->status == EDailyReport::PENDING_STATUS)->values()->toArray();
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->allocates = $allocates->values()->toArray();
                return $resource;
            });
            $teamEffort = $this->resourceService->mapTeamEffort($resources, $args['from'], $args['to'], $holiday);

            return $this->sendSuccess(['team_effort' => $teamEffort]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getProjectEffort(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->all();
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $decode = $this->decodeAccessToken(request()->bearerToken());
            $position = $decode->position;
            $division = $decode->division;
            $divisionName = $division->division_name ?? $division->name;
            if (in_array($position->name, ERole::GROUP_DL) && $divisionName != ERole::PQA && !$this->isSystemAdmin()) {
                $args['division_ids'][] = $division->division_id ?? $division->id;
            } else {
                $divisions = $this->userService->getDivisions(['is_active_project' => true]);
                $divisionIds = array_column($divisions, 'id');
                $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            }
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $args['pagination'] = false;

            $projects = $this->projectService->__getListProjects(
                array_merge($args, ['fields' => ['id', 'name', 'project_type'], 'disable_paginate' => 1])
            );
            $date = Carbon::createFromDate($request->month ?? date('Y-m-d'));
            $args['from'] = $date->firstOfMonth()->format('Y-m-d');
            $args['to'] = $date->lastOfMonth()->format('Y-m-d');

            $args['project_ids'] = array_column($projects, 'id');

            $paramsGetAllocation = [
                'project_ids' => $args['project_ids'] ?? [],
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByProjectIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'project_id' => $args['project_ids'] ?? [],
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0,
            ];
            $dailyReports = $this->dailyReportService->__getListReportOfUser($paramsGetReport);
            $dailyReports = collect($dailyReports);
            $activeProjectIds = $allocations
                ->map(fn($item) => $item->project->id)
                ->unique()->toArray();

            $projects = collect($projects)
                ->filter(function ($item) use ($activeProjectIds) {
                    return in_array($item->id, $activeProjectIds);
                })
                ->values();

            $projects->transform(function ($project) use ($dailyReports, $allocations) {
                unset($project->customers, $project->allocations, $project->pms, $project->pqas, $project->sellers, $project->approved_daily_reports, $project->stages);
                $reports = $dailyReports->filter(fn($item) => $item->project_id == $project->id);
                $project->approved_reports = $reports->filter(fn($item) => $item->status == EDailyReport::SUCCESS_STATUS)->values()->toArray();
                $project->waiting_reports = $reports->filter(fn($item) => $item->status == EDailyReport::PENDING_STATUS)->values()->toArray();
                $allocates = $allocations->filter(fn($item) => isset($item->project->id) && $item->project->id == $project->id);
                $project->allocates = $allocates->values()->toArray();
                return $project;
            });
            $projectEffort = $this->resourceService->mapProjectEffort($projects, $args['from'], $args['to'], $holiday);

            return $this->sendSuccess(['project_effort' => $projectEffort]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getResourceEffort(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'month',
                'effort_from',
                'effort_to',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type',
                'only_allocate'
            ]);
            $args['pagination'] = false;
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $decode = $this->decodeAccessToken(request()->bearerToken());
            $position = $decode->position;
            $division = $decode->division;
            $divisionName = $division->division_name ?? $division->name;
            if (in_array($position->name, ERole::GROUP_DL) && $divisionName != ERole::PQA && !$this->isSystemAdmin()) {
                $args['division_ids'][] = $division->division_id ?? $division->id;
            } else {
                $divisions = $this->userService->getDivisions(['is_active_project' => true]);
                $divisionIds = array_column($divisions, 'id');
                $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            }
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            if (isset($args['month'])) {
                $args['from'] = Carbon::parse($args['month'])->startOfMonth()->format('Y-m-d');
                $args['to'] = Carbon::parse($args['month'])->endOfMonth()->format('Y-m-d');
            } else {
                $args['from'] = Carbon::now()->startOfMonth()->format('Y-m-d');
                $args['to'] = Carbon::now()->endOfMonth()->format('Y-m-d');
            }
            $v2ApplyDate = config('app.v2_apply_date');

            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            /**
             * nếu filter theo mã dữ án hoặc loại dự án, chỉ lấy user được add vào dự án
             */
            if (isset($args['project_type'])) {
                $projects = $this->projectService->getProjects([
                    'disable_paginate' => 1,
                    'project_type' => $args['project_type']
                ]);
                $args['project_ids'] = array_column($projects, 'id');
            }

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }
            $resources = $this->resourceService->getResources(
                array_merge($args, [
                    'start_date' => $args['from'],
                    'end_date' => $args['to'],
                    'fields' => ['user_id', 'name', 'code', 'status', 'checkout_date', 'onboard_date', 'division', 'email']
                ])
            );
            $resources = collect($resources);

            $tmpResources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetAllocation = [
                'user_ids' => $args['user_ids'] ?? null,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['user_id', 'work_date', 'actual_time', 'status'],
                'get_project' => 0
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $paramsGetDaysLeave = [
                'user_ids' => $args['user_ids'] ?? null,
                'start_date' => $args['from'],
                'end_date' => Carbon::today()->format('Y-m-d'),
                'columns' => ['user_id', 'created_at', 'paid_leave']
            ];
            $daysLeave = $this->resourceService->__getDayLeave($paramsGetDaysLeave);
            $daysLeave = collect($daysLeave);

            $employeeOnLeave = $this->userService->__getEmployeeOnLeave([
                'from_date' => $args['from'],
                'to_date' => $args['to'],
            ]);
            $hoursOfOneShift = 2;
            $employeeOnLeave = collect($employeeOnLeave)
                ->filter(fn($item) => $item->paid_leave || $item->unpaid_leave)
                ->groupBy('user_id')
                ->map(function ($users) use ($hoursOfOneShift) {
                    $leaveHours = $users->map(function ($userLeave) use ($hoursOfOneShift) {
                        $paidLeaveHours = count(array_filter(explode(',', $userLeave->paid_leave))) * $hoursOfOneShift;
                        $unpaidLeaveHours = count(array_filter(explode(',', $userLeave->unpaid_leave))) * $hoursOfOneShift;

                        return $paidLeaveHours + $unpaidLeaveHours;
                    })->toArray();

                    return array_sum($leaveHours);
                })->toArray();

            $tmpResources->transform(function ($resource) use ($dailyReports, $allocations, $userUnpaidLeave, $daysLeave, $employeeOnLeave) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->reports_of_month = $reports->groupBy(fn($item) => Carbon::parse($item->work_date)->format('Y-m'))->toArray();
                $resource->daily_reports = $reports;
                $resource->approved_reports = $reports->filter(fn($item) => $item->status == EDailyReport::SUCCESS_STATUS)->values()->toArray() ?? [];
                $resource->waiting_reports = $reports->filter(fn($item) => $item->status == EDailyReport::PENDING_STATUS)->values()->toArray() ?? [];
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $projectsName = $allocates
                    ->filter(fn($allocate) => $allocate->user_id == $resource->user_id)
                    ->map(function ($allocate) {
                        if (isset($allocate->project)) return $allocate->project;
                    })
                    ->unique()
                    ->reduce(fn($prev, $item) => $item->name ?? $item . ', ' . $prev, '');
                $resource->project = rtrim($projectsName, ', ');
                $resource->allocates = $allocates->values()->toArray();
                $unpaidLeave = $userUnpaidLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->unpaid_leave = $unpaidLeave->values()->toArray();
                $resource->days_leave = $daysLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->leave_hours = $employeeOnLeave[$resource->user_id] ?? 0;
                return $resource;
            });
            $tmpResources = $this->resourceService->calculateResourceEffort($tmpResources, $holiday, $v2ApplyDate, $args['from'], $args['to']);

            return $this->sendSuccess(['resources' => $tmpResources]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getDivisionOverview(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();

            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'month',
                'effort_from',
                'effort_to',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type'
            ]);
            $date = Carbon::parse($args['month'] ?? Carbon::today());
            $args['from'] = $date->firstOfMonth()->format('Y-m-d');
            $args['to'] = $date->lastOfMonth()->format('Y-m-d');
            $args['pagination'] = false;
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $decode = $this->decodeAccessToken(request()->bearerToken());
            $position = $decode->position;
            $division = $decode->division;
            $divisionName = $division->division_name ?? $division->name;
            if (in_array($position->name, ERole::GROUP_DL) && $divisionName != ERole::PQA && !$this->isSystemAdmin()) {
                $args['division_ids'][] = $division->division_id ?? $division->id;
            } else {
                $divisions = $this->userService->getDivisions(['is_active_project' => true]);
                $divisionIds = array_column($divisions, 'id');
                $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            }
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $v2ApplyDate = config('app.v2_apply_date');

            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            /**
             * nếu filter theo mã dữ án hoặc loại dự án, chỉ lấy user được add vào dự án
             */
            if (isset($args['project_type'])) {
                $projects = $this->projectService->getProjects([
                    'disable_paginate' => 1,
                    'project_type' => $args['project_type']
                ]);
                $args['project_ids'] = array_column($projects, 'id');
            }

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }

            $resources = $this->resourceService->getResources(
                array_merge($args, [
                    'start_date' => $args['from'],
                    'end_date' => $args['to'],
                    'fields' => ['user_id', 'name', 'code', 'status', 'checkout_date', 'onboard_date']
                ])
            );
            $resources = collect($resources);
            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetAllocation = [
                'user_ids' => $args['user_ids'] ?? null,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
                'get_project' => 0
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports);

            $paramsGetDaysLeave = [
                'user_ids' => $args['user_ids'] ?? null,
                'start_date' => $args['from'],
                'end_date' => Carbon::today()->format('Y-m-d'),
                'columns' => ['user_id', 'created_at', 'paid_leave']
            ];
            $daysLeave = $this->resourceService->__getDayLeave($paramsGetDaysLeave);
            $daysLeave = collect($daysLeave);

            $resources->transform(function ($resource) use ($dailyReports, $allocations, $userUnpaidLeave, $daysLeave) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->daily_reports = $reports;
                $resource->approved_reports = $reports->filter(fn($item) => $item->status == EDailyReport::SUCCESS_STATUS)->values()->toArray() ?? [];
                $resource->waiting_reports = $reports->filter(fn($item) => $item->status == EDailyReport::PENDING_STATUS)->values()->toArray() ?? [];
                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->allocates = $allocates->values()->toArray();
                $unpaidLeave = $userUnpaidLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->unpaid_leave = $unpaidLeave->values()->toArray();
                $resource->days_leave = $daysLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                return $resource;
            });
            $divisionMetrics = $this->resourceService->calculateDivisionMetrics($resources, $holiday, $args['from'], $args['to']);

            return $this->sendSuccess(['division_metrics' => $divisionMetrics]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getContractTypes($args = [])
    {
        try {
            $contractTypes = $this->resourceService->getContractTypes($args);

            return $this->sendSuccess(['contract_types' => $contractTypes]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function getCalendarEffort(Request $request)
    {
        try {
            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'month',
                'effort_from',
                'effort_to',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type'
            ]);
            $args['pagination'] = true;
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $decode = $this->decodeAccessToken(request()->bearerToken());
            $position = $decode->position;
            $division = $decode->division;
            $divisionName = $division->division_name ?? $division->name;
            if (in_array($position->name, ERole::GROUP_DL) && $divisionName != ERole::PQA && !$this->isSystemAdmin()) {
                $args['division_ids'][] = $division->division_id ?? $division->id;
            } else {
                $divisions = $this->userService->getDivisions(['is_active_project' => true]);
                $divisionIds = array_column($divisions, 'id');
                $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            }
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            if (isset($args['from']) && isset($args['to'])) {
                $args['from'] = Carbon::createFromFormat('Y-m-d', $args['from'])->format('Y-m-d');
                $args['to'] = Carbon::createFromFormat('Y-m-d', $args['to'])->format('Y-m-d');
            } else {
                $args['from'] = Carbon::now()->startOfMonth()->format('Y-m-d');
                $args['to'] = Carbon::now()->endOfMonth()->format('Y-m-d');
            }
            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }
            $users = $this->resourceService->getResources(
                array_merge($args, [
                    'start_date' => $args['from'],
                    'end_date' => $args['to'],
                    'fields' => ['user_id', 'name', 'code', 'status', 'checkout_date', 'onboard_date', 'division', 'email']
                ])
            );
            $users = collect($users);

            if (empty($users)) return $this->sendSuccess(['resources' => $users]);
            $resources = collect($users['data']);
            $tmpResources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetAllocation = [
                'user_ids' => $args['user_ids'] ?? null,
                'from' => $args['from'],
                'to' => $args['to']
            ];
            $allocations = $this->allocationService->getAllocationByUserIds($paramsGetAllocation);
            $allocations = collect($allocations);

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['id', 'user_id', 'work_date', 'actual_time', 'status', 'title', 'project_id'],
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports)->map(function ($dailyReport) {
                $dailyReport->project_name = $dailyReport->project->name ?? '';
                unset($dailyReport->project, $dailyReport->project_id);
                return $dailyReport;
            });

            $paramsGetDaysLeave = [
                'user_ids' => $args['user_ids'] ?? null,
                'start_date' => $args['from'],
                'end_date' => Carbon::today()->format('Y-m-d'),
                'columns' => ['user_id', 'created_at', 'paid_leave']
            ];
            $daysLeave = $this->resourceService->__getDayLeave($paramsGetDaysLeave);
            $daysLeave = collect($daysLeave);

            $tmpResources->transform(function ($resource) use ($dailyReports, $allocations, $userUnpaidLeave, $daysLeave) {
                $reports = $dailyReports->filter(fn($item) => $item->user_id == $resource->user_id);
                $approvedReports = $reports->filter(fn($item) => $item->status == EDailyReport::SUCCESS_STATUS)
                    ->groupBy(fn($item) => Carbon::parse($item->work_date)->format('Y-m-d'))
                    ->map(fn($item) => $item->sum('actual_time'))
                    ->toArray();
                $resource->approved_reports = !empty($approvedReports) ? $approvedReports : null;
                $waitingReports = $reports->filter(fn($item) => $item->status == EDailyReport::PENDING_STATUS)
                    ->groupBy(fn($item) => Carbon::parse($item->work_date)->format('Y-m-d'))
                    ->map(fn($item) => $item->sum('actual_time'))
                    ->toArray();
                $resource->waiting_reports = !empty($waitingReports) ? $waitingReports : null;
                $resource->daily_reports = $reports->values()->toArray();

                $allocates = $allocations->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->allocates = $allocates->values()->toArray();
                $unpaidLeave = $userUnpaidLeave->filter(fn($item) => $item->user_id == $resource->user_id);
                $resource->unpaid_leave = $unpaidLeave->values()->toArray();
                $resource->days_leave = $daysLeave->filter(fn($item) => $item->user_id == $resource->user_id);

                $dateAllocate = [];
                foreach ($allocates as $allocate) {
                    $daysBetween = CarbonPeriod::create($allocate->start_date, $allocate->end_date)->filter(fn(Carbon $date) => $date->isWeekday());
                    foreach ($daysBetween as $date) {
                        if (isset($dateAllocate[$date->format('Y-m-d')])) {
                            $dateAllocate[$date->format('Y-m-d')] += $allocate->allocation / 100 * 8;
                        } else {
                            $dateAllocate[$date->format('Y-m-d')] = $allocate->allocation / 100 * 8;
                        }
                    }
                }
                $resource->calendar_effort = !empty($dateAllocate) ? $dateAllocate : null;
                return $resource;
            });
            $users->data = $tmpResources->values()->toArray();
            return $this->sendSuccess(['resources' => $users]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function exportLogWork(Request $request)
    {
        try {
            $args = $request->only([
                'page',
                'limit',
                'division',
                'project_id',
                'position',
                'skill',
                'rank',
                'from',
                'to',
                'month',
                'effort_from',
                'effort_to',
                'name',
                'position_ids',
                'division_ids',
                'skill_ids',
                'level_ids',
                'tab',
                'project_type'
            ]);
            $args['pagination'] = false;
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $positions = $this->userService->__getPositions(['is_active_project' => true]);
            $positionIds = array_column($positions, 'id');
            $args['position_ids'] = isset($args['position_ids']) ? $args['position_ids'] : $positionIds;
            $decode = $this->decodeAccessToken(request()->bearerToken());
            $position = $decode->position;
            $division = $decode->division;
            $divisionName = $division->division_name ?? $division->name;
            if (in_array($position->name, ERole::GROUP_DL) && $divisionName != ERole::PQA && !isset($args['division_ids'])) {
                $args['division_ids'][] = $division->division_id ?? $division->id;
            } else {
                $divisions = $this->userService->getDivisions(['is_active_project' => true]);
                $divisionIds = array_column($divisions, 'id');
                $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            }
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            if (isset($args['from']) && isset($args['to'])) {
                $args['from'] = Carbon::createFromFormat('Y-m-d', $args['from'])->format('Y-m-d');
                $args['to'] = Carbon::createFromFormat('Y-m-d', $args['to'])->format('Y-m-d');
            } else {
                $args['from'] = Carbon::now()->startOfMonth()->format('Y-m-d');
                $args['to'] = Carbon::now()->endOfMonth()->format('Y-m-d');
            }
            $userUnpaidLeave = $this->projectService->__getUserUnpaidLeaveByTime($args);
            $userUnpaidLeave = collect($userUnpaidLeave);

            if (isset($args['project_ids'])) {
                $members = $this->projectService->getProjectMembers($args);
                if (isset($members)) $args['user_ids'] = array_column($members, 'user_id');
                else $args['user_ids'] = [];
            }
            $resources = $this->resourceService->getResources(
                array_merge($args, [
                    'start_date' => $args['from'],
                    'end_date' => $args['to'],
                    'fields' => ['user_id', 'name', 'code', 'status', 'checkout_date', 'onboard_date', 'division', 'email']
                ])
            );
            $resources = collect($resources);
            if (empty($resources)) return $this->sendSuccess(['resources' => $resources]);

            $resources = $this->resourceService->filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args);
            $args['user_ids'] = $resources->pluck('user_id')->toArray();

            $paramsGetReport = [
                'user_ids' => $args['user_ids'] ?? null,
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['id', 'user_id', 'work_date', 'actual_time', 'status', 'title', 'project_id'],
            ];
            $dailyReports = $this->dailyReportService->getReports($paramsGetReport);
            $dailyReports = collect($dailyReports)->map(function ($dailyReport) {
                $dailyReport->project_name = $dailyReport->project->name ?? '';
                unset($dailyReport->project, $dailyReport->project_id);
                return $dailyReport;
            });
            $resources->transform(function ($resource) use ($dailyReports) {
                $reports = $dailyReports->filter(function ($item) use ($resource) {
                    if (
                        $item->user_id == $resource->user_id &&
                        ($item->status == EDailyReport::SUCCESS_STATUS || $item->status == EDailyReport::PENDING_STATUS)
                    ) {
                        return $item;
                    }
                })
                    ->groupBy(fn($item) => Carbon::parse($item->work_date)->format('d/m/y'))
                    ->map(fn($item) => $item->sum('actual_time'));
                $resource->daily_reports = $reports->toArray();

                return $resource;
            });

            $days = collect([]);
            $daysBetween = CarbonPeriod::create($args['from'], $args['to'])
                ->filter(fn(Carbon $date) => $date->isWeekday());
            foreach ($daysBetween as $date) {
                $days->push($date->format('d/m/y'));
            }

            return Excel::download(new LogWorkExport($resources, $days), 'daily-report-' . $args['from'] . '-' . $args['to'] . '.xlsx');


            return $this->sendSuccess(['resources' => $users]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }

    public function syncGoogleSheetsLogWork(Request $request)
    {
        try {
            $holiday = $this->resourceService->getHoliday();
            $args = $request->all();
            $divisions = $this->userService->getDivisions(['is_active_project' => true]);
            $divisionIds = array_column($divisions, 'id');
            $args['division_ids'] = isset($args['division_ids']) ? $args['division_ids'] : $divisionIds;
            if (isset($args['from']) && isset($args['to'])) {
                $args['from'] = Carbon::createFromFormat('Y-m-d', $args['from'])->format('Y-m-d');
                $args['to'] = Carbon::createFromFormat('Y-m-d', $args['to'])->format('Y-m-d');
            } else {
                $args['from'] = Carbon::now()->startOfMonth()->format('Y-m-d');
                $args['to'] = Carbon::now()->endOfMonth()->format('Y-m-d');
            }
            $from = $args['from'];
            $to = $args['to'];
            $month = Carbon::parse($from)->format('Y-m');
            if (isset($args['project_id'])) $args['project_ids'][] = $args['project_id'];
            $args['pagination'] = false;
            $paramsGetReport = [
                'from_date' => $args['from'] ?? null,
                'to_date' => $args['to'] ?? null,
                'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
                'columns' => ['user_id', 'project_id', 'work_date', 'actual_time', 'status'],
                'get_project' => 0,
            ];
            $dailyReports = $this->dailyReportService->__getListReportOfUser($paramsGetReport);
            $dailyReports = collect($dailyReports);
            $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday ?? [], [], null, null);
            $actualWorkingHours = $actualWorkingDays * 8;
            if (isset($args['type']) && $args['type'] == 'smooth') {
                $dailyReports = $dailyReports->groupBy(['user_id', function ($item) {
                    return Carbon::parse($item->work_date)->format('Y-m');
                }]);

                $dailyReports = $dailyReports->map(function ($userMonths) use ($actualWorkingHours) {
                    return $userMonths->map(function ($monthReports) use ($actualWorkingHours) {
                        $totalHours = $monthReports->sum('actual_time');

                        if ($totalHours > $actualWorkingHours) {
                            $ratio = $actualWorkingHours / $totalHours;
                            return $monthReports->map(function ($report) use ($ratio) {
                                $report->actual_time = round($report->actual_time * $ratio, 2);
                                return $report;
                            });
                        }
                        return $monthReports;
                    });
                })->flatten(2);
            }

            $userIds = $dailyReports->pluck('user_id')->toArray();
            $users = $this->resourceService->getResources([
                'user_ids' => $userIds,
                'fields' => ['user_id', 'name', 'position', 'division', 'code', 'checkout_date'],
            ]);

            $managers = $this->userService->getUsers([
                'position_ids' => ERole::GROUP_DL_IDS,
                'fields' => ['user_id', 'name', 'position', 'division', 'code'],
                'is_active' => EBase::ACTIVE,
            ]);

            $managers = collect($managers)->map(function ($manager) use ($dailyReports) {
                if (!$dailyReports->where('user_id', $manager->user_id)->count()) {
                    return [
                        [
                            'user' => $manager,
                            'effort' => 0.2
                        ],
                        [
                            'user' => $manager,
                            'effort' => 0.5
                        ],
                        [
                            'user' => $manager,
                            'effort' => 0.3
                        ]
                    ];
                }
                return [];
            })->flatten(1);

            $users = collect($users);
            $activeProjectIds = $dailyReports->pluck('project_id')->unique()->toArray();

            $args['project_ids'] = $activeProjectIds;
            unset($args['from'], $args['to'], $args['division_ids']);
            $args['project_type'] = [EProjectType::BY_CUSTOMER, EProjectType::IN_HOUSE];
            $args['status'] = [EProjectType::IN_PROGRESS, EProjectType::OPEN, EProjectType::PENDING, EProjectType::GUARANTEE];
            $projects = $this->projectService->__getListProjects(
                array_merge($args, [
                    'fields' => ['id', 'name', 'project_type', 'end_date', 'code', 'contract_type', 'legal', 'status', 'division_id'],
                    'disable_paginate' => 1,
                    'relation' => ['customers', 'projectMonthBudgets:project_id,month,budget'],
                ],)
            );

            $args['project_ids'] = array_column($projects, 'id');
            $paramsGetAllocation = [
                'project_ids' => $args['project_ids'] ?? [],
                'from' => $from,
                'to' => $to
            ];

            $allocations = $this->allocationService->getAllocationByProjectIds($paramsGetAllocation);
            $allocations = collect($allocations);
            $dailyReports = $dailyReports->filter(fn($item) => in_array($item->project_id, $args['project_ids']));

            $dailyReportNotEnough = $dailyReports->groupBy('user_id')
                ->map(function ($item) use ($actualWorkingHours, $users, $from) {
                    $user = $users->firstWhere('user_id', $item->first()->user_id);
                    $monthReport = Carbon::parse($from)->format('Y-m');
                    $maxHours = $actualWorkingHours;

                    if (isset($user->checkout_date) && Carbon::parse($user->checkout_date)->format('Y-m') === $monthReport) {
                        $workingDays = $this->getNumberOfWorkingDays($from, $user->checkout_date, $holiday ?? [], [], null, null);
                        $maxHours = $workingDays * 8;
                    }

                    return [
                        'user' => $user,
                        'effort' => ($maxHours - $item->sum('actual_time')) / $actualWorkingHours
                    ];
                })
                ->filter(fn($item) => $item['effort'] > 0)
                ->merge($managers);

            $projects = collect($projects)
                ->transform(function ($project) use ($dailyReports, $users, $allocations, $from, $to, $actualWorkingHours, $holiday) {
                    $budgetOnMonth = collect($project->project_month_budgets)->filter(fn($item) => Carbon::parse($item->month)->format('Y-m') == Carbon::parse($from)->format('Y-m'))->first();
                    unset($project->project_month_budgets, $project->pms, $project->pqas, $project->sellers, $project->approved_daily_reports, $project->stages, $project->billable_max);
                    $project->budget = $budgetOnMonth->budget ?? 0;
                    $project->contract_type = $project->contract_type == 1 ? 'Labo' : ($project->contract_type == 2 ? 'Fixed Price' : 'Revenue Share');
                    $project->legal = $project->legal == 1 ? 'Amela VN' : 'Amela JP';
                    $project->project_type = EProjectType::getActionString($project->project_type);
                    $project->status = EProjectType::getStatus($project->status);
                    $reports = $dailyReports->filter(fn($item) => $item->project_id == $project->id);
                    $project->reportsEffort = $reports->sum('actual_time') / $actualWorkingHours;
                    $allocates = $allocations->filter(fn($item) => $item->project->id == $project->id);
                    $project->allocateEffort = $this->resourceService->calAllocateEffort($allocates, $from, $to, $holiday ?? [], $resource->unpaid_leave ?? []) / $actualWorkingHours;
                    $project->gapEffort = $project->allocateEffort - $project->reportsEffort;
                    $project->allocateHours = $this->resourceService->calAllocateEffort($allocates, $from, $to, $holiday ?? [], $resource->unpaid_leave ?? []);

                    $reports = $reports
                        ->groupBy(fn($item) => Carbon::parse($item->work_date)->format('Y-m'))
                        ->map(function ($groupedLogWork) use ($users) {
                            return $groupedLogWork->groupBy('user_id')->map(function ($item) use ($users) {
                                $userId = $item->first()->user_id;
                                $user = $users->firstWhere('user_id', $userId);
                                return [
                                    'code' => $user->code,
                                    'name' => $user->name,
                                    'position' => $user->position->name,
                                    'division' => $user->division->name,
                                    'hours' => $item->sum('actual_time')
                                ];
                            })->values();
                        });
                    $project->reports = $reports;
                    $project->customers = collect(($project->customers))->pluck('name')->implode(', ');
                    return $project;
                });
            if (isset($args['type']) && $args['type'] == 'smooth') {
                $dailyReportNotEnough->each(function ($report) use (&$projects, $actualWorkingHours, $month) {
                    $matchingProject = $projects->first(function ($project) use ($report) {
                        return $project->budget > 1 &&
                            $project->gapEffort <= $report['effort'] + 1;
                    });

                    if (!$matchingProject || !isset($matchingProject->reports[$month])) {
                        return;
                    }

                    // Update project effort and add report in one go
                    $matchingProject->gapEffort += $report['effort'];
                    $matchingProject->reports[$month][] = [
                        'code' => $report['user']->code,
                        'name' => $report['user']->name,
                        'position' => $report['user']->position->name,
                        'division' => $report['user']->division->name,
                        'hours' => $actualWorkingHours * $report['effort'],
                        'fake' => $report['effort'] * $actualWorkingHours
                    ];
                });
            }
            return $this->sendSuccess(['projects' => $projects]);
        } catch (\Exception $e) {
            return $this->getError($e);
        }
    }
}
