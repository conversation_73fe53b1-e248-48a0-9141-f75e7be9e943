<?php

namespace App\Services;

use App\Enums\EDailyReport;
use App\Traits\ExtraFunctions;
use App\Traits\Helper;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class DailyReportService
{
    use ProjectServiceSupport;
    use UserServiceSupport;
    use Helper;
    use ExtraFunctions;

    private UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function getListDailyReport($args = [])
    {
        $dailyReports = $this->__getListDailyReport($args);
        $arrUser = collect($this->userService->getUsersWithFilter())->keyBy('user_id')->all();
        $holidays = $this->getHoliday();
        $group = $args['group'] ?? EDailyReport::GROUP_BY_USER;

        $pendingQuantity = $dailyReports->pending_quantity ?? 0;
        $successQuantity = $dailyReports->success_quantity ?? 0;
        $rejectQuantity = $dailyReports->reject_quantity ?? 0;

        $totalTimeSuccessInDate = $dailyReports->total_time_success_in_date ?? [];
        $totalAllocateTimeInDate = json_decode(
            json_encode($dailyReports->total_allocate_time_in_date ?? []),
            true
        );

        $totalAllocateTimeInDate = collect($totalAllocateTimeInDate)
            ->map(function ($allocateTimesUser) use ($holidays) {
                return collect($allocateTimesUser)->filter(function ($value, $date) use ($holidays) {
                    return (bool)$this->getNumberOfWorkingDays($date, $date, $holidays);
                });
            });

        $userHasReport = $this->mapInfoUserByIds($dailyReports->user_has_report ?? [], $arrUser);
        $userNotReport = $this->mapInfoUserByIds($dailyReports->user_not_report ?? [], $arrUser);
        $dailyReports = $this->mapUserInfoToListReport($dailyReports->daily_reports, $arrUser, $group);

        return [
            'data' => $dailyReports,
            'allocate_quantity' => $totalAllocateTimeInDate->flatten()->sum(),
            'user_has_report' => $userHasReport,
            'user_not_report' => $userNotReport,
            'total_time_success_in_date' => $totalTimeSuccessInDate,
            'total_allocate_time_in_date' => $totalAllocateTimeInDate,
            'pending_quantity' => $pendingQuantity,
            'success_quantity' => $successQuantity,
            'reject_quantity' => $rejectQuantity
        ];
    }

    public function getListReportOfUser($args = [])
    {
        $userDailyReports = $this->__getListReportOfUser($args);
        $users = $this->__getUsers(['user_ids' => (array)$args['user_id']]);
        $arrUser = collect($users)->keyBy('user_id')->toArray();

        return $this->mapUserInfoToListReport($userDailyReports, $arrUser);
    }

    public function mapInfoUserByIds($userIds, $arrUser)
    {
        return array_map(function ($userId) use ($arrUser) {
            return [
                'id' => $userId,
                'name' => $arrUser[$userId]->name ?? null,
                'email' => $arrUser[$userId]->email ?? null,
                'avatar' => $arrUser[$userId]->avatar ?? null
            ];
        }, $userIds);
    }

    public function mapUserInfoToListReport($dailyReports, $arrUser, $group = EDailyReport::GROUP_BY_USER)
    {
        if ($group == EDailyReport::GROUP_BY_USER) {
            $dailyReports = collect($dailyReports)->map(function ($dailyReport) use ($arrUser) {
                return $this->mapUserDataToReport($arrUser[$dailyReport->user_id] ?? [], $dailyReport);
            });
        }

        if ($group == EDailyReport::GROUP_BY_DATE) {
            $dailyReports = collect($dailyReports)->map(function ($dailyReport) use ($arrUser) {
                $dailyReport->reports = collect($dailyReport->reports)->map(function ($report) use ($arrUser) {
                    return $this->mapUserDataToReport($arrUser[$report->user_id] ?? [], $report);
                });

                return $dailyReport;
            });
        }

        return $dailyReports;
    }

    public function mapUserDataToReport($arrUser, $report)
    {
        $report->user_name = $arrUser->name ?? null;
        $report->user_email = $arrUser->email ?? null;
        $report->code = $arrUser->code ?? null;
        $report->avatar = $arrUser->avatar ?? null;

        return $report;
    }

    public function changeStatus($args)
    {
        return $this->__changeStatus($args);
    }

    public function getDailyReports($args = [])
    {
        $userDailyReports = $this->__getListReportOfUser($args);
        $arrUser = collect($this->__getUsers())->keyBy('user_id')->toArray();
        $this->mapUserInfoToListReport($userDailyReports, $arrUser);

        return collect($userDailyReports)->map(function ($item) {
            return [
                'user_code' => $item->code,
                'user_email' => $item->user_email,
                'user_name' => $item->user_name,
                'title' => $item->title,
                'work_date' => carbon($item->work_date)->format('d/m/Y'),
                'actual_time' => $item->actual_time,
                'coefficient' => $item->coefficient,
                'actual_time_coefficient' => $item->actual_time * $item->coefficient,
                'link_backlog' => $item->link_backlog,
                'status' => EDailyReport::ALL_STATUS_TEXT[$item->status] ?? '',
                'description' => $this->removeStringSpace($item->description),
                'reject_reason' => $this->removeStringSpace($item->reject_reason)
            ];
        })
            ->toArray();
    }

    public function getReports($paramsGetReports)
    {
        return $this->__getReports($paramsGetReports);
    }

    public function getDailyReportByUserIds($args = [])
    {
        return $this->__getDailyReportByUserIds($args);
    }

    public function getDailyReportByProjectIds($args = [])
    {
        return $this->__getDailyReportByProjectIds($args);
    }
}
