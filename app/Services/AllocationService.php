<?php

namespace App\Services;

use App\Enums\ERole;
use App\Repositories\UserRepository;
use App\Traits\ExtraFunctions;
use App\Traits\HrServiceSupport;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class AllocationService
{
    use ProjectServiceSupport;
    use UserServiceSupport;
    use HrServiceSupport;
    use ExtraFunctions;

    private UserRepository $userRepository;
    private UserService $userService;

    public function __construct(
        UserService    $userService,
        UserRepository $userRepository
    ) {
        $this->userService = $userService;
        $this->userRepository = $userRepository;
    }

    public function getCustomers($args)
    {
        return $this->__getCustomers($args);
    }

    public function getAllocations($projectId, $stageId, $args)
    {
        $allocations = $this->__getAllocations($projectId, $stageId, $args);
        $allocations = collect($allocations);
        $skills = collect($this->__getSkill())->pluck('name', 'id')->toArray();
        $userIds = $allocations->pluck('user_id')->unique()->toArray();
        $query['fields'] = ['user_id', 'name', 'code', 'avatar', 'email', 'level', 'position'];
        $query['user_ids'] = $userIds;
        $users = $this->__getUsers($query);
        $users = collect($users);
        if ($users->isEmpty()) {
            return $allocations;
        }
        $allocations->map(function ($allocation) use ($users, $skills) {
            $user = $users->firstWhere('user_id', $allocation->user_id);
            $allocation->skill_name = $skills[$allocation->skill_id] ?? '';

            return $allocation->user = $user;
        });

        return $allocations;
    }

    public function store($args, $projectId, $stageId)
    {
        $users = $this->userService->getUsers(['user_ids' => $args['user_ids']]);

        $args['coefficients'] = collect($users)
            ->map(function ($item) {
                $coefficient = $item->level->coefficient ?? 1;
                if (isset($item->position) && $item->position == ERole::INTERN) $coefficient = 0;
                return [
                    'user_id' => $item->user_id,
                    'coefficient' => $coefficient
                ];
            })
            ->pluck('coefficient', 'user_id')
            ->toArray();

        return $this->__createAllocation($args, $projectId, $stageId);
    }

    public function update($args, $projectId, $stageId, $allocationId)
    {
        return $this->__updateAllocation($args, $projectId, $stageId, $allocationId);
    }

    public function destroy($args, $projectId, $stageId, $allocationId)
    {
        return $this->__deleteAllocation($args, $projectId, $stageId, $allocationId);
    }

    public function getAllocationByUserIds($args = [])
    {
        return $this->__getAllocationByUserIds($args);
    }

    public function getAllocationByProjectIds($args = [])
    {
        return $this->__getAllocationByProjectIds($args);
    }

    public function getAllocationInMonth($args = [])
    {
        return $this->__getAllocationInMonth($args);
    }

    public function getAllocationInYear($args = [])
    {
        return $this->__getAllocationInYear($args);
    }

    public function checkOnboardCheckoutDateDuplicates($args): bool
    {
        $users = $this->userRepository->getUsersWithFilter(
            ['user_id' => $args['user_ids']]
        )
            ->toArray();

        $users = collect($users)->keyBy('user_id')->toArray();

        return collect($args['user_ids'])->contains(function ($userId, $key) use ($args, $users) {
            $user = $users[$userId] ?? null;

            return $this->isDuplicateOnboardCheckoutDate(
                $user['onboard_date'] ?? null,
                $user['checkout_date'] ?? null,
                $args['start_dates'][$key],
                $args['end_dates'][$key]
            );
        });
    }

    public function isDuplicateOnboardCheckoutDate($onBoardDate, $checkoutDate, $startDate, $endDate): bool
    {
        return !empty($onBoardDate) && $onBoardDate > $startDate
            || !empty($onBoardDate) && $onBoardDate > $endDate
            || !empty($checkoutDate) && $checkoutDate < $startDate
            || !empty($checkoutDate) && $checkoutDate < $endDate;
    }
}
