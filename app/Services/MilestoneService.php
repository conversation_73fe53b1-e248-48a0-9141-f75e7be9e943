<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;

class MilestoneService
{
    use ProjectServiceSupport;

    public function __construct()
    {
    }

    public function store($args, $projectId, $stageId)
    {
        return $this->__createMilestone($args, $projectId, $stageId);
    }

    public function update($args, $projectId, $stageId, $id)
    {
        return $this->__updateMilestone($args, $projectId, $stageId, $id);
    }

    public function destroy($args, $projectId, $stageId, $id)
    {
        return $this->__deleteMilestone($args, $projectId, $stageId, $id);
    }
}
