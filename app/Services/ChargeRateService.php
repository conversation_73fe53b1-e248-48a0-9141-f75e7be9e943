<?php

namespace App\Services;

use App\Enums\EBase;
use App\Enums\EContractType;
use App\Enums\EDivision;
use App\Enums\EProjectType;
use App\Traits\DivisionSupport;
use App\Traits\ExtraFunctions;
use App\Traits\Helper;
use App\Traits\ProjectServiceSupport;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;

class ChargeRateService
{
    use ProjectServiceSupport;
    use Helper;
    use ExtraFunctions;
    use DivisionSupport;

    private UserService $userService;
    private ResourceService $resourceService;

    public function __construct(
        UserService     $userService,
        ResourceService $resourceService
    ) {
        $this->userService = $userService;
        $this->resourceService = $resourceService;
    }

    private function getAllMonths($startDate, $endDate): array
    {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $start->modify('first day of this month');
        $end->modify('first day of next month');
        $interval = DateInterval::createFromDateString('1 month');
        $period = new DatePeriod($start, $interval, $end);
        $months = [];
        foreach ($period as $month) {
            $months[] = $month->format('Y-m');
        }

        return $months;
    }

    public function getDivisionChargeRateStatisticsChart($args = [], $divEffort = [], $holidays, $divisions): array
    {
        $chargeRates = $this->getDivisionChargeRateStatistics($args, $divEffort ?? [], $holidays ?? null, $divisions ?? []);
        $allMonth = $this->getAllMonths($args['from'], $args['to']);

        $result = array_map(function ($rate) use ($allMonth) {
            return array_map(fn ($month) => [
                'id' => $rate['division_id'],
                'name' => $rate['division_name'],
                'month' => __('message.charge_rate.month') . ' ' . Carbon::create($month)->format('m'),
                'value' => $rate['charge_rate'][$month] ?? 0
            ], $allMonth);
        }, $chargeRates);

        $result[] = $this->calAllChargeRate($chargeRates, $allMonth);

        return $result;
    }

    private function calAllChargeRate($chargeRates, $allMonth): array
    {
        $sumAllRevenue = collect($allMonth)->mapWithKeys(function ($month) use ($chargeRates) {
            $value = collect($chargeRates)->sum(function ($rate) use ($month) {
                $revenueDistribution = $rate['revenue_distribution'][$month] ?? 0;
                $outsourcingRentalCost = $rate['outsourcing_rental_costs'][$month] ?? 0;

                return $revenueDistribution - $outsourcingRentalCost;
            });

            return [$month => $value];
        })
            ->toArray();

        $sumAllAllocate = collect($allMonth)->mapWithKeys(function ($month) use ($chargeRates) {
            return [$month => collect($chargeRates)->sum(fn ($rate) => $rate['allocates'][$month] ?? 0)];
        })
            ->toArray();

        return collect($allMonth)
            ->map(function ($month) use ($sumAllRevenue, $sumAllAllocate) {
                $revenue = $sumAllRevenue[$month] ?? 0;
                $allocate = $sumAllAllocate[$month] ?? 0;
                $chargeRate = $sumAllAllocate[$month] == 0 ? 0 : ($revenue / $allocate) * 100;

                return [
                    'id' => null,
                    'name' => __('message.charge_rate.company'),
                    'month' => __('message.charge_rate.month') . ' ' . Carbon::create($month)->format('m'),
                    'value' => $chargeRate
                ];
            })
            ->toArray();
    }

    public function getDivisionChargeRateStatistics($args = [], $divEffort = [], $holidays, $divisions): array
    {
        $chargeRates = (array)$this->__getChargeRateStatistics($args);
        $projectBudgets = $chargeRates['project_budgets'];
        $rentalCosts = collect($chargeRates['rental_costs'] ?? []);
        $internalRentalCosts = $rentalCosts->filter(fn ($rentalCost) => $rentalCost->division_id != EBase::FREELANCER_RENTAL_COST['value'])->values();
        $divisionPartnerCosts = $rentalCosts->filter(fn ($rentalCost) => $rentalCost->division_id == EBase::FREELANCER_RENTAL_COST['value'])->values();

        $divisionIds = array_keys($divisions);
        $allMonth = $this->getAllMonths($args['from'], $args['to']);

        $divisionBudges = $this->processDivisionBudgets($projectBudgets);
        $divisionInternalRevenues = $this->calDivisionRevenues($rentalCosts);
        $divisionInternalRentalCost = $this->calDivisionRentalCosts($internalRentalCosts);
        $divisionPartnerCosts = $this->calDivisionRentalCosts($divisionPartnerCosts);
        return $this->processChargeRate(
            $divisionIds,
            $divisionBudges,
            $divisionInternalRevenues,
            $divisionInternalRentalCost,
            $divisionPartnerCosts,
            $divisions,
            $divEffort,
            $allMonth
        );
    }

    private function processChargeRate(
        $divisionIds,
        $divisionBudges,
        $divisionInternalRevenues,
        $divisionInternalRentalCost,
        $divisionPartnerCosts,
        $divisions,
        $divEffort,
        $allMonth
    ): array {

        $chargeRates = array_map(function ($divisionId) use (
            $divEffort,
            $divisionBudges,
            $divisionInternalRevenues,
            $divisionInternalRentalCost,
            $divisionPartnerCosts,
            $allMonth,
            $divisions
        ) {
            $chargeRate = collect($allMonth)
                ->mapWithKeys(function ($month) use (
                    $divEffort,
                    $divisionBudges,
                    $divisionInternalRevenues,
                    $divisionInternalRentalCost,
                    $divisionPartnerCosts,
                    $divisionId
                ) {
                    $allocate = $divEffort[$divisionId][$month] ?? 0;
                    $budget = $divisionBudges[$divisionId][$month] ?? 0;
                    $internalRevenue = $divisionInternalRevenues[$divisionId][$month] ?? 0;
                    $internalRentalCost = $divisionInternalRentalCost[$divisionId][$month] ?? 0;
                    $partnerCost = $divisionPartnerCosts[$divisionId][$month] ?? 0;

                    if ($allocate == 0) {
                        return [$month => 0];
                    }

                    return [$month => (($budget + $internalRevenue - $internalRentalCost - $partnerCost) / $allocate) * 100];
                })
                ->toArray();

            return [
                'division_id' => $divisionId,
                'division_name' => $divisions[$divisionId] ?? '',
                'allocates' => array_filter($divEffort[$divisionId] ?? []),
                'revenue_distribution' => array_filter($divisionBudges[$divisionId] ?? []),
                'internal_revenues' => array_filter($divisionInternalRevenues[$divisionId] ?? []),
                'internal_rental_costs' => array_filter($divisionInternalRentalCost[$divisionId] ?? []),
                'outsourcing_rental_costs' => array_filter($divisionPartnerCosts[$divisionId] ?? []),
                'charge_rate' => array_filter($chargeRate)
            ];
        }, $divisionIds);

        return array_values($chargeRates);
    }

    public function calDivisionRevenues($rentalCosts): array
    {
        return collect($rentalCosts)
            ->groupBy('division_id')
            ->map(function ($divisionRentalCost) {
                return $divisionRentalCost
                    ->pluck('month_costs')
                    ->flatten(1)
                    ->groupBy('month')
                    ->map(fn ($monthCosts) => $monthCosts->sum(fn ($monthCost) => $monthCost->cost))
                    ->toArray();
            })
            ->toArray();
    }

    public function calDivisionRentalCosts($rentalCosts): array
    {
        return $rentalCosts->groupBy(fn ($rentalCost) => $rentalCost->project->division_id)
            ->map(function ($divisionRentalCost) {
                return $divisionRentalCost
                    ->pluck('month_costs')
                    ->flatten(1)
                    ->groupBy('month')
                    ->map(fn ($monthCosts) => $monthCosts->sum(fn ($monthCost) => $monthCost->cost))
                    ->toArray();
            })
            ->toArray();
    }

    private function calPartnerCosts($divisionIds, $partnerAllocates, $holidays, $allMonth): array
    {
        $users = collect($this->userService
            ->getUsersWithFilter(['fields' => ['division', 'contract', 'user_id']]))
            ->keyBy('user_id')->toArray();
        $divisionPartnerCosts = [];

        foreach ($divisionIds as $divisionId) {
            $partnerAllocate = collect($partnerAllocates)
                ->filter(function ($allocate) use ($divisionId, $users) {
                    $user = $users[$allocate->user_id] ?? null;
                    $userContractType = @$user['contract']['contract_category_id'] ?? null;

                    return $userContractType == EContractType::RENTAL_CONTRACT && $allocate->division_id == $divisionId;
                });

            foreach ($allMonth as $month) {
                $from = Carbon::create($month)->firstOfMonth()->toDateString();
                $to = Carbon::create($month)->lastOfMonth()->toDateString();
                $divisionPartnerCosts[$divisionId][$month] = $this->calDivisionAllocate($partnerAllocate, $holidays, $from, $to);
            }
        }

        return  $divisionPartnerCosts;
    }

    private function calDivisionAllocate($allocates, $holidays, $from, $to): float|int
    {
        return collect($allocates)
            ->groupBy('user_id')
            ->map(function ($userAllocates) use ($from, $to, $holidays) {
                return $this->calUserAllocate($userAllocates, $from, $to, $holidays);
            })
            ->sum();
    }

    private function processDivisionBudgets($projectBudgets): array
    {
        return collect($projectBudgets)
            ->groupBy(fn ($budget) => @$budget->project->division_id)
            ->map(function ($divisionBudgets) {
                return $divisionBudgets->groupBy('month')
                    ->mapWithKeys(function ($budgets, $month) {
                        return [$month => $budgets->sum('budget')];
                    });
            })
            ->toArray();
    }

    private function calUserAllocate($userAllocates, $from, $to, $holidays): float|int
    {
        $userWorkingHours = $this->getNumberOfWorkingDays(
            $from,
            $to,
            $holidays
        ) * 8;

        if ($userWorkingHours == 0) {
            return 0;
        }

        $userAllocateEffort = $this->resourceService->calAllocateEffort(
            collect($userAllocates),
            $from,
            $to,
            $holidays
        );

        return $userAllocateEffort / $userWorkingHours;
    }

    public function getResourceRentalCosts($args = []): array
    {
        $args['year'] = $args['year'] ?? now()->year;
        $args['relations'] = ['project:id,name,division_id,project_type', 'monthCosts'];
        $args['project_types'] = [EProjectType::BY_CUSTOMER, EProjectType::OPPORTUNITY, EProjectType::TRAINING];
        $resourceRentalCosts = $this->__genListRentalCost($args);
        $divisions = array_column((array)$this->__getDivisions(), 'name', 'id');

        return array_map(function ($item) use ($divisions) {
            $item['division_name'] = $divisions[$item['project']['division_id']] ?? null;
            $item['rent_from_division_name'] = $divisions[$item['division_id']] ?? null;

            return $item;
        }, $resourceRentalCosts);
    }

    public function updateResourceRentalCosts($args = [])
    {
        return $this->__updateOrCreateResourceRentalCost($args);
    }

    public function getDivisionChargeRates($args = [])
    {
        return $this->__getDivisionChargeRates($args);
    }

    public function getDivisionManMonthList($args = [])
    {
        return $this->__getDivisionManMonthList($args);
    }

    public function generateMonthsInYear($year)
    {
        return collect(range(1, 12))->map(function ($month) use ($year) {
            return $month < 10 ? $year . '-0' . $month : $year . '-' . $month;
        })->toArray();
    }

    public function getDivisionChargeRateChart($args = []): array
    {
        $chargeRates = $this->__getDivisionChargeRates($args);

        if ($args['year'] > env('TIME_APPLY_CHARGE_RATE_MODULE_DIVISION', 2023)) {
            $chargeRates = array_filter($chargeRates, function ($chargeRate) {
                return ! in_array($chargeRate['division_id'], EDivision::HIDE_DIVISIONS);
            });

            $chargeRates = array_values($chargeRates);
        }

        $months = $this->generateMonthsInYear($args['year']);

        $result = array_map(function ($rate) use ($months) {
            return array_map(fn($month) => [
                'id' => $rate['division_id'],
                'name' => $rate['division_name'],
                'month' => __('message.charge_rate.month') . ' ' . Carbon::parse($month)->format('m'),
                'value' => $rate['charge_rate'][$month] ?? 0
            ], $months);
        }, $chargeRates);

        if ($result) {
            $result[] = $this->calTotalChargeRates($chargeRates, $months);
        }

        return $result;
    }

    private function calTotalChargeRates($chargeRates, $months): array
    {
        $sumAllRevenue = collect($months)->mapWithKeys(function ($month) use ($chargeRates) {
            $value = collect($chargeRates)->sum(function ($rate) use ($month) {
                $revenueDistribution = $rate['revenue_distribution'][$month] ?? 0;
                $outsourcingRentalCost = $rate['outsourcing_rental_costs'][$month] ?? 0;

                return $revenueDistribution - $outsourcingRentalCost;
            });

            return [$month => $value];
        })
            ->toArray();

        $sumAllAllocate = collect($months)->mapWithKeys(function ($month) use ($chargeRates) {
            return [$month => collect($chargeRates)->sum(fn($rate) => $rate['allocates'][$month] ?? 0)];
        })
            ->toArray();

        return collect($months)
            ->map(function ($month) use ($sumAllRevenue, $sumAllAllocate) {
                $revenue = $sumAllRevenue[$month] ?? 0;
                $allocate = $sumAllAllocate[$month] ?? 0;

                $chargeRate = $sumAllAllocate[$month] == 0
                    ?
                    0
                    :
                    ($revenue / $allocate) * 100;

                return [
                    'id' => null,
                    'name' => __('message.charge_rate.company'),
                    'month' => __('message.charge_rate.month') . ' ' . Carbon::parse($month)->format('m'),
                    'value' => $chargeRate
                ];
            })
            ->toArray();
    }
}
