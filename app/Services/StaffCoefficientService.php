<?php

namespace App\Services;

use App\Traits\DivisionSupport;
use App\Repositories\UserRepository;

class StaffCoefficientService
{
    use DivisionSupport;
    private UserRepository $userRepository;
    public function __construct(
        UserRepository $userRepository
    ) {
        $this->userRepository = $userRepository;
    }

    public function getListStaffCoefficient($args)
    {
        $staffCoefficients = $this->__getStaffCoefficients($args);
        
        $userIds = array_column($staffCoefficients['data'], 'user_id');

        $users = $this->userRepository->getUsersWithFilter(
            ['user_id' => $userIds]
        );

        $userById = $users->keyBy('user_id')->all();

        $staffCoefficients['data'] = collect($staffCoefficients['data'])->map(function ($item) use ($userById) {
            $userId = $item['user_id'];
            $user = $userById[$userId];
            $item['name'] = $user->name ?? null;
            $item['email'] = $user->email ?? null;
            $item['code'] = $user->code ?? null;
            $item['division'] = $user->division ?? null;
            $item['team'] = $user->team ?? null;

            return $item;
        });

        return $staffCoefficients;
    }

    public function store($args)
    {
        return $this->__createStaffCoefficient($args);
    }

    public function update($args, $id)
    {
        return $this->__updateStaffCoefficient($args, $id);
    }

    public function delete($id)
    {
        return $this->__deleteStaffCoefficient($id);
    }
}
