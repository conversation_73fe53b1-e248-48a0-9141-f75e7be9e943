<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;

class RulePenaltyService
{
    use ProjectServiceSupport;

    public function getRules()
    {
        $rules = collect($this->__getRules());

        return $rules->where('parent_id', null)->map(function ($item) use ($rules) {
            $item->children = $rules->where('parent_id', $item->id)->values();
            unset($item->parent_id);

            return $item;
        })->values();
    }
}
