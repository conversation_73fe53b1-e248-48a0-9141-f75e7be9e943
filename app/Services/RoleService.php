<?php

namespace App\Services;

use App\Enums\EBase;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;
use Illuminate\Support\Facades\Redis;

class RoleService
{
    use UserServiceSupport;
    use ProjectServiceSupport;

    public function getRoles($args = [])
    {
        return $this->__getRoles($args);
    }

    public function update($args, $id)
    {
        return $this->__updateRole($args, $id);
    }

    public function getUserPermission($userId)
    {
        $permissions = json_decode(Redis::get($userId), 1);
        $permissionInProject = json_decode(Redis::get(EBase::IN_PROJECT.$userId), 1);
        if (empty($permissions)) {
            $permissions = EBase::DEFAULT_ROUTES;
        } else {
            $permissions = array_merge(EBase::DEFAULT_ROUTES, $permissions);
        }
        $superAdmin = explode(',', env('ADMIN_USER_IDS'));

        return [
            'is_admin' => in_array($userId, $superAdmin),
            'site_permissions' => $permissions,
            'project_permissions' => $permissionInProject,
        ];
    }
}
