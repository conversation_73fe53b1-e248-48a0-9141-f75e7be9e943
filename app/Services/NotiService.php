<?php

namespace App\Services;

use App\Enums\ENoti;
use App\Jobs\NotiJob;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class NotiService
{
    use UserServiceSupport;
    use ProjectServiceSupport;

    public function sendNoti($projectId, $request, $messageKey, $type, $roles, $member = null)
    {
        $user_id = $request->user_id;
        $users = $this->__getUsers(['user_ids' => [$user_id]]);
        if (@$users[0]->name) {
            $user = $users[0];
        } else {
            return;
        }

        $userIds = $this->__getMemberIdsByRoles($projectId, $roles);
        $userIds = array_unique($userIds);
        $project = $this->__getProject($projectId);

        $message = __($messageKey, ['sender' => $user->name, 'project' => $project->name, 'member' => @$member->name]);

        foreach ($userIds as $userId) {
            dispatch(
                new NotiJob(
                    $projectId,
                    $userId,
                    $message,
                    $user->avatar,
                    $type,
                    ENoti::getDetailNoti($project->id, $type)
                )
            )->delay(now()->addSecond())->afterCommit();
        }
    }
}
