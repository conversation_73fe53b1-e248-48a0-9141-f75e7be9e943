<?php

namespace App\Services;

use App\Enums\ERequirementChangeImpactedRole;
use App\Enums\ERequirementChangeImport;
use App\Enums\ERequirementChangePriority;
use App\Enums\ERequirementChangeStatus;
use App\Enums\ERequirementChangeType;
use App\Enums\EStageType;
use App\Imports\RequirementChangeImport;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class RequirementChangeService
{
    use ProjectServiceSupport, UserServiceSupport;

    private FileService $fileService;

    /**
     * @var StageService
     */
    private StageService $stageService;

    public function __construct(
        StageService $stageService,
        FileService $fileService
    ) {
        $this->fileService = $fileService;
        $this->stageService = $stageService;
    }

    public function store($args, $projectId)
    {
        $fileCustomers = [];
        $fileFinished = [];
        $evidenceCustomers = $args['evidence_customer'] ?? null;
        $evidenceFinished = $args['evidence_finished'] ?? null;

        if ($evidenceCustomers != null) {
            $fileEvidenceCustomers = [];
            foreach ($evidenceCustomers as $file) {
                $filePath = 'projects/documents';
                $nameUrl = $this->fileService->uploadFileS3($file, $filePath);
                $fileName = $file->getClientOriginalName();
                $fileEvidence = [
                    'url' => $nameUrl,
                    'file_name' => $fileName,
                ];
                array_push($fileEvidenceCustomers, $fileEvidence);
            }
            $fileCustomers['evidence_customer'] = $fileEvidenceCustomers;
        }

        if ($evidenceFinished != null) {
            $fileEvidenceFinished = [];
            foreach ($evidenceFinished as $file) {
                $filePath = 'projects/documents';
                $nameUrl = $this->fileService->uploadFileS3($file, $filePath);
                $fileName = $file->getClientOriginalName();
                $fileEvidence = [
                    'url' => $nameUrl,
                    'file_name' => $fileName,
                ];
                array_push($fileEvidenceFinished, $fileEvidence);
            }
            $fileFinished['evidence_finished'] = $fileEvidenceFinished;
        }
        $params = array_merge($args, $fileCustomers, $fileFinished);

        return $this->__createRequirementChange($params, $projectId);
    }

    public function update($args, $projectId, $requirementChangeId)
    {
        $fileCustomers = [];
        $fileFinished = [];
        $evidenceCustomers = $args['evidence_customer'] ?? null;
        $evidenceFinished = $args['evidence_finished'] ?? null;
        if ($evidenceCustomers != null) {
            $fileEvidenceCustomers = [];
            foreach ($evidenceCustomers as $file) {
                $filePath = 'projects/documents';
                $nameUrl = $this->fileService->uploadFileS3($file, $filePath);
                $fileName = $file->getClientOriginalName();
                $fileEvidence = [
                    'url' => $nameUrl,
                    'file_name' => $fileName,
                ];
                array_push($fileEvidenceCustomers, $fileEvidence);
            }
            $fileCustomers['evidence_customer'] = $fileEvidenceCustomers;
        }

        if ($evidenceFinished != null) {
            $fileEvidenceFinished = [];
            foreach ($evidenceFinished as $file) {
                $filePath = 'projects/documents';
                $nameUrl = $this->fileService->uploadFileS3($file, $filePath);
                $fileName = $file->getClientOriginalName();
                $fileEvidence = [
                    'url' => $nameUrl,
                    'file_name' => $fileName,
                ];
                array_push($fileEvidenceFinished, $fileEvidence);
            }
            $fileFinished['evidence_finished'] = $fileEvidenceFinished;
        }
        $params = array_merge($args, $fileCustomers, $fileFinished);

        return $this->__updateRequirementChange(
            $params,
            $projectId,
            $requirementChangeId
        );
    }

    public function destroy($projectId, $args)
    {
        return $this->__deleteRequirementChange(
            $projectId,
            $args
        );
    }

    public function show($projectId, $requirementChangeId)
    {
        return $this->__showRequirementChange($projectId, $requirementChangeId);
    }

    public function getListRequirementChange($args, $projectId)
    {
        return $this->__getListRequirementChange($args, $projectId);
    }

    public function getDataImport($file, $projectId)
    {
        $data = Excel::toArray(new RequirementChangeImport, $file);

        return $this->validateDataImport($data[0], $projectId);
    }

    private function validateDataImport($dataExcel, $projectId)
    {
        $checkFileImport = $this->validateFileImport($dataExcel);
        if (
            $checkFileImport == ERequirementChangeImport::ERROR_INVALID_FORMAT
        ) {
            return ['errors' => __('field_import_invalid')];
        } elseif (
            $checkFileImport == ERequirementChangeImport::ERROR_EMPTY_DATA
        ) {
            return ['errors' => __('field_import_required_data')];
        }
        $columnNames = $this->getColumnName();
        $errors = [];
        $dataImport = [];
        $stageOptions = $this->stageService->getListStage([], $projectId);

        foreach ($dataExcel as $index => $requirementChange) {
            if ($requirementChange[$columnNames['col_no']]) {
                $line = $index + ERequirementChangeImport::BEGIN_ROW;
                $temp = [];
                $temp['project_id'] = $projectId;

                $validateTitle = $this->validateText(
                    $requirementChange[$columnNames['col_title']],
                    true,
                    50
                );
                if ($validateTitle['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateTitle['message'],
                        'column' => ERequirementChangeImport::COLUMN_TITLE,
                    ];
                }
                $temp['title'] = $validateTitle['text'];

                $validateContent = $this->validateText(
                    $requirementChange[$columnNames['col_content']],
                    true,
                    1000
                );
                if ($validateContent['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateContent['message'],
                        'column' => ERequirementChangeImport::COLUMN_CONTENT,
                    ];
                }
                $temp['content'] = $validateContent['text'];

                $validateDetailContent = $this->validateText(
                    $requirementChange[$columnNames['col_detail_content']],
                    true,
                    1000
                );

                if ($validateDetailContent['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateDetailContent['message'],
                        'column' => ERequirementChangeImport::COLUMN_CONTENT,
                    ];
                }
                $temp['content'] = $validateDetailContent['text'];

                $validateImpact = $this->validateText(
                    $requirementChange[$columnNames['col_impact']],
                    true,
                    1000
                );

                if ($validateImpact['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateImpact['message'],
                        'column' => ERequirementChangeImport::COLUMN_IMPACT,
                    ];
                }
                $temp['impact'] = $validateImpact['text'];

                $validateImpactRoles = $this->validateText(
                    $requirementChange[$columnNames['col_impact_roles']],
                    true,
                    1000
                );

                if ($validateImpactRoles['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateImpactRoles['message'],
                        'column' => ERequirementChangeImport::COLUMN_IMPACT_ROLES,
                    ];
                }
                $temp['impact_roles'] = $validateImpactRoles['text'];

                $validateNote = $this->validateText(
                    $requirementChange[$columnNames['col_note']],
                    false,
                    1000
                );
                if ($validateNote['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateNote['message'],
                        'column' => ERequirementChangeImport::COLUMN_NOTE,
                    ];
                }
                $temp['note'] = $requirementChange[$columnNames['col_note']];

                $validateCost = $this->validateCost(
                    $requirementChange[$columnNames['col_cost']]
                );
                if ($validateCost['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateCost['message'],
                        'column' => ERequirementChangeImport::COLUMN_COST,
                    ];
                }
                $temp['cost'] = $validateCost['cost'];

                $validateType = $this->validateType(
                    $requirementChange[$columnNames['col_type']]
                );
                if ($validateType['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateType['message'],
                        'column' => ERequirementChangeImport::COLUMN_TYPE,
                    ];
                }
                $temp['type'] = $validateType['type'];

                $validateRequestDate = $this->validateDate(
                    $requirementChange[$columnNames['col_request_date']]
                );
                if ($validateRequestDate['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateRequestDate['message'],
                        'column' => ERequirementChangeImport::COLUMN_REQUEST_DATE,
                    ];
                }
                $temp['request_date'] = $validateRequestDate['date'];

                $validateStatus = $this->validateStatus(
                    $requirementChange[$columnNames['col_status']]
                );
                if ($validateStatus['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateStatus['message'],
                        'column' => ERequirementChangeImport::COLUMN_STATUS,
                    ];
                }
                $temp['status'] = $validateStatus['status'];

                $validateStage = $this->validateStage(
                    $requirementChange[$columnNames['col_stage']],
                    $stageOptions
                );
                if ($validateStage['message']) {
                    $errors[] = [
                        'line' => $line,
                        'message' => $validateStage['message'],
                        'column' => ERequirementChangeImport::COLUMN_STAGE,
                    ];
                }
                $temp['stage_id'] = $validateStage['stage_id'];

                $temp['created_at'] = now()->format('Y-m-d H:i:s');
                $temp['updated_at'] = now()->format('Y-m-d H:i:s');
                $dataImport[] = $temp;
            } else {
                return [
                    'errors' => $errors,
                    'data_import' => $dataImport,
                ];
            }
        }

        if (empty($errors)) {
            return [
                'data_import' => $dataImport,
            ];
        }

        return [
            'errors' => $errors,
            'data_import' => $dataImport,
        ];
    }

    private function getColumnName()
    {
        return [
            'col_no' => 'no',
            'col_type' => 'type',
            'col_request_date' => 'request_date',
            'col_title' => 'title',
            'col_content' => 'content',
            'col_detail_content' => 'detailed_content',
            'col_impact' => 'impact',
            'col_impact_roles' => 'impact_roles',
            'col_status' => 'status',
            'col_stage' => 'stage',
            'col_cost' => 'cost',
            'col_note' => 'note',
        ];
    }

    private function validateFileImport($dataExcel)
    {
        $rowFirst = 0;
        if (isset($dataExcel[$rowFirst])) {
            $fieldFile = array_keys($dataExcel[$rowFirst]);
            $fieldFile = array_filter($fieldFile, function ($field) {
                return $field;
            });
            $columnNames = $this->getColumnName();
            $fieldCommon = array_values($columnNames);
            $check = false;
            foreach ($fieldCommon as $key => $field) {
                if ($field != ($fieldFile[$key] ?? '')) {
                    $check = true;
                }
            }
            if ($check) {
                return ERequirementChangeImport::ERROR_INVALID_FORMAT;
            }

            return ERequirementChangeImport::FILE_VALID_FORMAT;
        }

        return ERequirementChangeImport::ERROR_EMPTY_DATA;
    }

    private function validateType($type)
    {
        $message = '';
        if (!$type) {
            $message = __('required');
        }
        $check = false;
        foreach (ERequirementChangeType::getMasterData() as $key => $value) {
            if (mb_strtolower(trim($key)) == mb_strtolower($type)) {
                $type = $value;
                $check = true;
                break;
            }
        }
        if (!$check) {
            $message = __('error_type');
        }

        return ['message' => $message, 'type' => $type];
    }

    private function validatePriority($priority)
    {

        $message = '';
        if (!$priority) {
            $message = __('required');
        }
        $check = false;
        foreach (ERequirementChangePriority::getMasterData()
            as $key => $value) {
            if (mb_strtolower(trim($key)) == mb_strtolower($priority)) {
                $priority = $value;
                $check = true;
                break;
            }
        }
        if (!$check) {
            $message = __('error_priority');
        }

        return ['message' => $message, 'priority' => $priority];
    }

    private function validateDate($date)
    {
        $message = '';
        $date = implode('-', explode('/', $date));
        if (is_string($date)) {
            if (!$this->isDate($date)) {
                $message = __('error_format_date');
            }

            $date = date('Y-m-d', strtotime($date));
        } else {
            $date = $date
                ? \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject(
                    $date
                )->format('Y-m-d')
                : null;
        }

        return ['message' => $message, 'date' => $date];
    }

    private function isDate($value, $format = 'Y-m-d')
    {
        if (!$value) {
            return false;
        } else {
            $date = date_parse(trim($value));
            if ($date['error_count'] == 0 && $date['warning_count'] == 0) {
                return checkdate($date['month'], $date['day'], $date['year']);
            } else {
                return false;
            }
        }
    }

    private function validateImpactedRole($impactedRoles)
    {
        $message = '';
        $check = false;
        foreach (ERequirementChangeImpactedRole::getMasterData()
            as $key => $value) {
            if (mb_strtolower(trim($key)) == mb_strtolower($impactedRoles)) {
                $impactedRoles = $value;
                $check = true;
                break;
            }
        }
        if (!$check) {
            $message = __('error_impacted_roles');
        }

        return ['message' => $message, 'impacted_roles' => $impactedRoles];
    }

    private function validateStatus($status)
    {
        $message = '';
        $check = false;
        foreach (ERequirementChangeStatus::getMasterData() as $key => $value) {
            if (mb_strtolower(trim($key)) == mb_strtolower($status)) {
                $status = $value;
                $check = true;
                break;
            }
        }
        if (!$check) {
            $message = __('error_status', ['string' => ucwords(implode(', ', array_keys(ERequirementChangeStatus::getMasterData())))]);
        }

        return ['message' => $message, 'status' => $status];
    }

    private function validateStage($stage, $stageOptions)
    {
        $stage = trim($stage);
        if (empty($stage)) {
            return ['message' => '', 'stage_id' => null];
        }

        $message = '';
        $check = false;
        $types = EStageType::getMasterData();

        $values = explode(' ', $stage);
        $label = strtolower(trim($values[0] ?? ''));
        $type = $types[$label] ?? 0;
        $version = trim($values[1] ?? 0);

        $stage_id = null;
        foreach ($stageOptions as $item) {
            $item = json_decode(json_encode($item), true);
            if (trim($item['type']) == $type && trim($item['version']) == $version) {
                $stage_id = $item['id'];
                $check = true;
                break;
            }
        }
        if (!$check) {
            $message = __('error_stage');
        }

        return ['message' => $message, 'stage_id' => $stage_id];
    }

    public function import($dataImport, $projectId)
    {
        return $this->__importRequirementChange($dataImport, $projectId);
    }

    private function validateText($text, $isRequired = false, $max = 50)
    {
        $message = '';
        if (!$text && $isRequired) {
            $message = __('required');
        } elseif (Str::length($text) > $max) {
            $message = trans('validation.max_string_characters', ['max' => $max]);
        }

        return ['message' => $message, 'text' => $text];
    }

    private function validateCost($cost)
    {
        $message = '';
        if ($cost) {
            if ($cost < 0.01) {
                $message = __('min_value_0.01');
            }
            if ($cost > 1000) {
                $message = __('max_value_1000');
            }
        }

        return ['message' => $message, 'cost' => $cost];
    }
}
