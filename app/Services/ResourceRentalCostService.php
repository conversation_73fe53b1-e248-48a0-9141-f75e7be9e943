<?php

namespace App\Services;

use App\Enums\EBase;
use App\Traits\Helper;
use App\Traits\HrServiceSupport;
use App\Traits\ProjectServiceSupport;

class ResourceRentalCostService
{
    use Helper, ProjectServiceSupport, HrServiceSupport;

    public function getList($args)
    {
        $rentalCosts = (array)$this->__getResourceRentalCosts($args);
        $divisions = array_column((array)$this->__getDivisions(), 'name', 'id');

        return array_map(function ($rentalCost) use ($divisions) {
            $rentalCost->division_name = $divisions[$rentalCost->division_id] ?? null;
            if ($rentalCost->division_id == EBase::FREELANCER_RENTAL_COST['value']) {
                $rentalCost->division_name = EBase::FREELANCER_RENTAL_COST['label'];
            }
            return $rentalCost;
        }, $rentalCosts);
    }

    public function updateOrCreate($args)
    {
        return $this->__updateOrCreateResourceRentalCost($args);
    }

    public function destroy($id)
    {
        return $this->__deleteResourceRentalCost($id);
    }
}
