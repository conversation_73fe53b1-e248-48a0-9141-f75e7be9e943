<?php

namespace App\Services;

use App\Enums\EWeeklyReport;
use App\Exports\WeeklyReportExport;
use App\Traits\HrServiceSupport;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;
use Maatwebsite\Excel\Facades\Excel;

class WeeklyReportService
{
    use ProjectServiceSupport, UserServiceSupport, HrServiceSupport;

    public function getAll($args)
    {
        $weeklyReports = $this->__getWeeklyReports($args);
        $projectIds = collect($weeklyReports->data)->pluck('project_id')->filter()->unique()->toArray();
        $userIds = collect($weeklyReports->data)->flatMap(function ($report) {
            return [
                $report->pm_id ?? null,
                $report->dm_id ?? null,
                $report->pqa_id ?? null,
                $report->pic_id ?? null,
                $report->action_id ?? null,
            ];
        })->filter()->unique()->toArray();
        $users = $userIds ? $this->__getUsers(['user_ids' => $userIds, 'fields' => ['user_id', 'name', 'avatar']]) : [];
        $users = collect($users)->keyBy('user_id');
        $projects = $projectIds ? $this->__getRawProjects(['project_ids' => $projectIds, 'relations' => 'customers:id,name', 'fields' => ['id', 'name']]) : [];
        $projects = collect($projects)->keyBy('id');

        collect($weeklyReports->data)->transform(function ($weeklyReport) use ($users, $projects) {
            $weeklyReport->project = $projects->get($weeklyReport->project_id) ?: null;
            $weeklyReport->pm = $users->get($weeklyReport->pm_id) ?: null;
            $weeklyReport->dm = $users->get($weeklyReport->dm_id) ?: null;
            $weeklyReport->pqa = $users->get($weeklyReport->pqa_id) ?: null;
            $weeklyReport->pic = $users->get($weeklyReport->pic_id) ?: null;
            $weeklyReport->action = $users->get($weeklyReport->action_id) ?: null;

            return $weeklyReport;
        });

        return $weeklyReports;
    }

    public function export($args)
    {
        $args['pagination'] = false;
        $weeklyReports = collect($this->__getWeeklyReports($args));
        $projectIds = $weeklyReports->pluck('project_id')->filter()->unique()->toArray();
        $userIds = $weeklyReports->flatMap(function ($report) {
            return [
                $report->pm_id ?? null,
                $report->dm_id ?? null,
                $report->pqa_id ?? null,
                $report->pic_id ?? null,
                $report->action_id ?? null,
            ];
        })->filter()->unique()->toArray();
        $users = $userIds ? $this->__getUsers(['user_ids' => $userIds, 'fields' => ['user_id', 'name']]) : [];
        $users = collect($users)->keyBy('user_id');
        $projects = $projectIds ? $this->__getRawProjects(['project_ids' => $projectIds, 'relations' => 'customers:id,name', 'fields' => ['id', 'name', 'code', 'division_id']]) : [];
        $projects = collect($projects)->keyBy('id');
        $weeklyReports = $weeklyReports->transform(function ($weeklyReport) use ($projects) {
            $weeklyReport->project = $projects->get($weeklyReport->project_id);

            return $weeklyReport;
        });
        $status = EWeeklyReport::getStatus();
        $stageType = EWeeklyReport::getStageType();
        $statusAction = EWeeklyReport::getStatusAction();
        $divisions = collect($this->__getDivisions())->pluck('name', 'id')->toArray();
        $weeklyReports = $weeklyReports->groupBy(fn($report) => $report->project->division_id)->flatten()->transform(function ($weeklyReport) use ($users, $status, $stageType, $statusAction, $divisions) {
            $customerName = $weeklyReport->project->customers 
                ? implode(chr(10), array_column($weeklyReport->project->customers, 'name')) 
                : '';

            return [
                "division_name" => $divisions[$weeklyReport->project->division_id] ?? '',
                "project_code" => $weeklyReport->project->code,
                "project_name" => $weeklyReport->project->name,
                "customer_name" => $customerName,
                "pm_name" => $users->get($weeklyReport->pm_id)->name ?? '',
                "dm_name" => $users->get($weeklyReport->dm_id)->name ?? '',
                "pqa_name" => $users->get($weeklyReport->pqa_id)->name ?? '',
                "pic_name" => $users->get($weeklyReport->pic_id)->name ?? '',
                "stage_type" => $stageType[$weeklyReport->stage_type] ?? '',
                "pm_status" => $status[$weeklyReport->pm_status] ?? '',
                "dm_status" => $status[$weeklyReport->dm_status] ?? '',
                "pqa_status" => $status[$weeklyReport->pqa_status] ?? '',
                "pic_status" => $status[$weeklyReport->pic_status] ?? '',
                "pm_note" => $weeklyReport->pm_note ?? '',
                "dm_note" => $weeklyReport->dm_note ?? '',
                "pqa_note" => $weeklyReport->pqa_note ?? '',
                "pic_note" => $weeklyReport->pic_note ?? '',
                "action_content" => $weeklyReport->action_content ?? '',
                "action_name" => $users->get($weeklyReport->action_id)->name ?? '',
                "action_status" => $statusAction[$weeklyReport->action_status] ?? '',
            ];
        })->toArray();

        return Excel::download(new WeeklyReportExport($weeklyReports), EWeeklyReport::FILE_NAME_EXPORT);
    }
}
