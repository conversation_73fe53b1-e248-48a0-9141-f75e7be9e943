<?php

namespace App\Services;

use App\Traits\ExtraFunctions;
use App\Traits\HrServiceSupport;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class StageService
{
    use ProjectServiceSupport, UserServiceSupport, HrServiceSupport, ExtraFunctions;

    public function __construct()
    {
    }

    public function store($args, $projectId)
    {
        return $this->__createStage($args, $projectId);
    }

    public function update($args, $projectId, $stageId)
    {
        return $this->__updateStage($args, $projectId, $stageId);
    }

    public function destroy($args, $projectId, $stageId)
    {
        return $this->__deleteStage($args, $projectId, $stageId);
    }

    public function show($projectId, $stageId)
    {
        $stage = $this->__showStage($projectId, $stageId);

        if (!empty($stage->allocations)) {
            $allocations = collect($stage->allocations);
            $allocateUserIds['user_ids'] = $allocations->pluck('user_id')->toArray();

            $users = $this->__getUsers($allocateUserIds);

            $memberIds = collect();
            $allocations->map(function ($allocation) use ($users, &$memberIds) {
                $user = collect($users)->first(fn ($user) => $allocation->user_id == $user->user_id);
                $allocation->user = collect($user)->only('name', 'email', 'position', 'level');
                if ($user) {
                    $memberIds->push($user->user_id);
                }

                return $allocation;
            });
            $stage->member_ids = $memberIds;
        }

        return $stage;
    }

    public function getListStage($args, $projectId)
    {
        return $this->__getListStage($args, $projectId);
    }

    public function getSchedule($projectId)
    {
        return $this->__getSchedule($projectId);
    }

    public function clone($args, $projectId, $stageId)
    {
        return $this->__cloneStage($args, $projectId, $stageId);
    }
}
