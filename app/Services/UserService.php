<?php

namespace App\Services;

use App\Repositories\UserRepository;
use App\Traits\HrServiceSupport;
use App\Traits\UserServiceSupport;

class UserService
{
    use UserServiceSupport, HrServiceSupport;

    protected UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getUsers($args)
    {
        return $this->__getUsers($args);
    }

    public function getPositions()
    {
        return $this->__getPositions();
    }

    public function getDivisions($args)
    {
        return $this->__getDivisions($args);
    }

    public function getTeams($args)
    {
        return $this->__getTeams($args);
    }

    public function updateDivisionStatus($args, $id)
    {
        return $this->__updateDivisionStatus($args, $id);
    }

    public function updatePositionStatus($args, $id)
    {
        return $this->__updatePositionStatus($args, $id);
    }

    public function getUsersWithFilter($filters = [])
    {
        return $this->userRepository->getUsersWithFilter($filters, $filters['fields'] ?? ['*']);
    }
}
