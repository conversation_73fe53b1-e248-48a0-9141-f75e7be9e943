<?php

namespace App\Services;

use App\Enums\ERole;
use App\Traits\Helper;
use App\Traits\HrServiceSupport;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class ViolationReportService
{
    use ProjectServiceSupport, UserServiceSupport, HrServiceSupport, Helper;

    private $fileService;

    public function __construct(FileService $fileService)
    {
        $this->fileService = $fileService;
    }

    public function getListViolationReport($args)
    {
        $user = $this->getCurrentUserInfo();
        $adminIds = explode(',', env('ADMIN_USER_IDS')) ?? [];
        if ($user->position->name == ERole::PQA || in_array($user->user_id, $adminIds)) $args['is_admin'] = true;
        $args['user_id'] = $user->user_id;
        $violationReports = $this->__getViolationReport($args);
        $teamIds = array_column($violationReports->data, 'team_id');
        $teams = $teamIds ? array_column((array)$this->__getTeams(['team_ids' => $teamIds]), 'name', 'id') : [];
        $creatorIds = array_column($violationReports->data, 'creator_id');
        $handlerIds  = array_column($violationReports->data, 'handler_id');
        $violatorIds = array_column($violationReports->data, 'violator_id');
        $userIds = array_unique(array_merge($creatorIds, $handlerIds, $violatorIds));
        $users = $this->__getUsers(
            ['user_ids' => $userIds, 'fields' => ['user_id', 'name', 'avatar']]
        );
        $users = collect($users)->keyBy('user_id')->toArray();
        collect($violationReports->data)->map(function ($violationReport) use($users, $teams) {
            $violationReport->project = @$violationReport->project[0];
            $violationReport->rule = @$violationReport->rule[0];
            $violationReport->creator = @$users[$violationReport->creator_id];
            $violationReport->handler = @$users[$violationReport->handler_id];
            $violationReport->violator = @$users[$violationReport->violator_id];
            $violationReport->team_name = @$teams[$violationReport->team_id];
        });

        return $violationReports;
    }
 
    public function store($args)
    {
        $args['creator_id'] = $args['user_id'];
        $args = $this->processData($args);

        return $this->__createViolationReport($args);
    }

    public function update($args, $id)
    {
        $args = $this->processData($args);

        return $this->__updateViolationReport($args, $id);
    }

    private function processData($args)
    {
        $filePath = 'projects/reports';
        if ($evidence = @$args['evidence']) {
            $args += [
                'evidence_url' => $this->fileService->uploadFileS3($evidence, $filePath),
                'evidence_file_name' => $evidence->getClientOriginalName(),
            ];
        }
        if ($result = @$args['result']) {
            $args += [
                'result_url' => $this->fileService->uploadFileS3($result, $filePath),
                'result_file_name' => $result->getClientOriginalName(),
            ];
        }

        return $args;
    }
}
