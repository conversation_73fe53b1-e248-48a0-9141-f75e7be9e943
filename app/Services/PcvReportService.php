<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class PcvReportService
{
    use ProjectServiceSupport, UserServiceSupport;

    public function __construct()
    {
    }

    public function getPcvReports($projectId)
    {
        $pcvReports = $this->__getPcvReports($projectId);
        $pcvReports = collect($pcvReports);
        $users['user_ids'] = $pcvReports->pluck('user_id')->toArray();
        $users['fields'] = ['user_id', 'name', 'avatar'];
        $users = $this->__getUsers($users);
        $pcvReports->map(function ($pcvReport) use ($users) {
            $user = array_filter($users, fn ($user) => $pcvReport->user_id == $user->user_id);
            $user = array_values($user);

            return $pcvReport->user = collect(...$user)->toArray();
        });

        return $pcvReports;
    }

    public function store($args, $projectId)
    {
        $args['project_id'] = $projectId;
        $customer = $this->__createPcvReport($args, $projectId);

        return $customer;
    }

    public function update($args, $projectId, $pcvReportId)
    {
        return $this->__updatePcvReport($args, $projectId, $pcvReportId);
    }

    public function destroy($projectId, $reportId)
    {
        return $this->__deletePcvReport($projectId, $reportId);
    }
}
