<?php

namespace App\Services;

use App\Enums\EBase;
use App\Enums\EDailyReport;
use App\Enums\EDefect;
use App\Enums\EFunction;
use App\Enums\EProjectPerformanceImport;
use App\Enums\EProjectType;
use App\Imports\ProjectPerformanceImport;
use App\Traits\ExtraFunctions;
use App\Traits\Helper;
use App\Traits\HrServiceSupport;
use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;
use App\Traits\WorkflowServiceSupport;
use App\Http\Responses\EEChartModels\EEChartColumnNode;
use App\Http\Responses\EEChartModels\EEChartLineNode;
use Carbon\Carbon;
use Cassandra\Index;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class ProjectService
{
    use UserServiceSupport, HrServiceSupport, ProjectServiceSupport, ExtraFunctions, WorkflowServiceSupport, Helper;

    private $resourceService;

    private $customerService;

    private $userService;

    private $allocationService;

    private $dailyReportService;

    public function __construct(
        ResourceService $resourceService,
        CustomerService $customerService,
        UserService $userService,
        AllocationService $allocationService,
        DailyReportService $dailyReportService
    ) {
        $this->resourceService = $resourceService;
        $this->customerService = $customerService;
        $this->userService = $userService;
        $this->allocationService = $allocationService;
        $this->dailyReportService = $dailyReportService;
    }

    public function store($args)
    {
        $project = $this->__createProject($args);

        return $project;
    }

    public function getListProject($args)
    {
        $holiday = $this->getHoliday();
        $projects = $this->__getListProject($args);
        if (isset($projects->data)) $projectIds = array_column($projects->data, 'id');

        if (!empty($projects->data)) {
            $userIds['user_ids'] = [];
            $divisionIds['division_ids'] = [];
            foreach ($projects->data as $project) {
                array_push(
                    $userIds['user_ids'],
                    ...array_column($project->allocations, 'user_id'),
                    ...$project->pm_ids,
                );
                array_push($divisionIds['division_ids'], $project->division_id);
            }
            $tmpProject = $projects->data;
            $userIds['user_ids'] = array_values(array_unique($userIds['user_ids']));
            $divisionIds['division_ids'] = array_values(array_unique($divisionIds['division_ids']));
            $users = $this->__getUsers($userIds);
            $divisions = $this->__getDivisions($divisionIds);
            $overtimes = $this->__getOvertime(['project_ids' => $projectIds ?? []]);
            /**
             * 1. filter list pm belongs to project
             * 2. map -> take only pm name
             * 3. array_values -> format key
             */
            foreach ($tmpProject as $project) {
                $projectManager = array_filter($users, fn ($user) => in_array($user->user_id, $project->pm_ids));
                $overtimes = collect($overtimes);
                $otMM = $this->calOTToManMonth($overtimes, $project->id);

                $ot = $overtimes->filter(fn ($overtime) => $overtime->project_id == $project->id)
                    ->reduce(fn ($prev, $item) => $prev + $item->ot, 0);

                $project->project_managers = array_values(
                    array_map(fn ($user) => [
                        'id' => $user->user_id, 'name' => $user->name, 'avatar' => $user->avatar,
                    ], $projectManager)
                );
                $project->division = collect($divisions)->where('id', $project->division_id)->first();
                $allocates = collect($project->allocations);

                $calendarEffort = $this->calCalenderEffortWithCoefficient($allocates, $holiday);
                $allocateInTheFuture = $allocates->map(function ($allocate) {
                    $now = Carbon::now();
                    if ($now->gt($allocate->end_date)) return null;
                    if ($now->lt($allocate->end_date)) {
                        $allocate->start_date = $now->format('Y-m-d');
                        return $allocate;
                    }
                })->filter();
                $effortInTheFuture = $this->calCalenderEffortWithCoefficient($allocateInTheFuture, $holiday);
                // log work from daily report
                $dailyReportEffort = $this->calDailyReportEffort($project, $holiday);

                $project->ot = $ot ?? 0;
                // $actualEffort = (($dailyReportEffort + $effortInTheFuture) + $otMM) ?? 0;
                $actualEffort = ($calendarEffort + $otMM) ?? 0;
                $project->calendar_effort = $calendarEffort;
                $project->actual_effort = $actualEffort;
                $project->daily_report_effort = $dailyReportEffort;
                $project->effort_in_the_future = $effortInTheFuture;
                $project->effort_ot = $otMM;
                if (!empty($actualEffort)) {
                    $ee = $project->billable / $actualEffort;
                    $project->ee = $ee;
                }
                unset($project->allocations);
                unset($project->approved_daily_reports);
                unset($project->stages);
            }
            $projects->data = $tmpProject;
        }

        return $projects;
    }

    /**
     * total daily report until today
     * @param $project
     * @param $holiday
     * @return float
     */
    public function calDailyReportEffort($project, $holiday)
    {
        $today = Carbon::today();
        $totalDailyReport = collect($project->approved_daily_reports)
            ->filter(function ($report) use ($today) {
                return Carbon::parse($report->work_date)->lessThan($today);
            })
            ->sum('actual_time');

        // project start date
        $stages = $project->stages;
        $from = empty($project->start_date)
            ? $this->getStartDateOfOldestStage($stages)
            : Carbon::parse($project->start_date)->format('Y-m-d');
        if (empty($from) || $today <= Carbon::parse($from)) {
            return 0;
        }

        $to = Carbon::today()->format('Y-m-d');
        $actualWorkingDay = $this->numberOfWorkingDays($from, $to, $holiday);
        if ($actualWorkingDay == 0) return 0;

        return ($totalDailyReport / ($actualWorkingDay * EBase::WORKING_DAY_HOURS));
    }

    /**
     * @param $stages
     * @return null
     */
    public function getStartDateOfOldestStage($stages)
    {
        if (empty($stages)) {
            return null;
        }

        $from = Carbon::today()->format('Y-m-d');

        $oldestStartDate = collect($stages)->reduce(function ($oldest, $stage) use ($from) {
            $start_date = $stage->replan_start_date ?? $stage->start_date;
            $parsed_date = Carbon::parse($start_date);

            return $parsed_date < $from ? $parsed_date : $oldest;
        }, Carbon::parse($from));

        return $oldestStartDate->format('Y-m-d');
    }

    public function calOTToManMonth($overtimes, $projectId)
    {
        return $overtimes
            ->filter(fn ($overtime) => $overtime->project_id == $projectId)
            ->map(function ($overtime) {
                $startDate = Carbon::parse($overtime->date)->firstOfMonth()->format('Y-m-d');
                $endDate = Carbon::parse($overtime->date)->endOfMonth()->format('Y-m-d');
                $workingDays = $this->numberOfWorkingDays($startDate, $endDate);
                return ($overtime->ot / 8) / $workingDays;
            })
            ->reduce(fn ($prev, $item) => $prev + $item, 0);
    }

    public function calCalenderEffort($allocates, $holiday)
    {
        return $allocates
            ->map(function ($allocate) use ($holiday) {
                $from = Carbon::createFromFormat('Y-m-d', $allocate->start_date);
                $to = Carbon::createFromFormat('Y-m-d', $allocate->end_date);
                $months = $this->resourceService->getMonthsBetween($from, $to);
                $allocate->effort = collect($months)->reduce(
                    function ($prev, $item) use ($allocate, $holiday) {
                        $allocatedOfMonth = $this->calManMonthSingleMonth(
                            $allocate->start_date,
                            $allocate->end_date,
                            $allocate->allocation,
                            $item,
                            $holiday
                        );

                        return $prev + $allocatedOfMonth / 100;
                    },
                    0
                );

                return $allocate;
            })
            ->reduce(fn ($prev, $item) => $prev + $item->effort, 0);
    }

    public function mapCalenderEffort($allocates, $holiday, $startDate, $endDate)
    {
        return $allocates
            ->map(function ($allocate) use ($holiday, $startDate, $endDate) {
                $timeRangeStartDate = max($startDate, $allocate->start_date);
                $timeRangeEndDate = min($endDate, $allocate->end_date);
                $from = Carbon::createFromFormat('Y-m-d', $timeRangeStartDate);
                $to = Carbon::createFromFormat('Y-m-d', $timeRangeEndDate);
                $months = $this->resourceService->getMonthsBetween($from, $to);
                $allocate->effort = collect($months)->reduce(
                    function ($prev, $item) use ($allocate, $holiday, $timeRangeStartDate, $timeRangeEndDate) {
                        $allocatedOfMonth = $this->calManMonthSingleMonth(
                            $allocate->start_date,
                            $allocate->end_date,
                            $allocate->allocation,
                            $item,
                            $holiday
                        );

                        return $prev + $allocatedOfMonth / 100;
                    },
                    0
                );
                $allocate->effort_with_coefficient = $allocate->effort * $allocate->coefficient;
                return $allocate;
            });
    }

    public function calCalenderEffortWithCoefficient($allocates, $holiday)
    {
        return $allocates
            ->map(function ($allocate) use ($holiday) {
                $from = Carbon::createFromFormat('Y-m-d', $allocate->start_date);
                $to = Carbon::createFromFormat('Y-m-d', $allocate->end_date);
                $allocate->man_day = $this->numberOfWorkingDays($from, $to, $holiday);

                $months = $this->resourceService->getMonthsBetween($from, $to);
                $allocate->effort = collect($months)->reduce(
                    function ($prev, $item) use ($allocate, $holiday) {
                        $allocatedOfMonth = $this->calManMonthSingleMonth(
                            $allocate->start_date,
                            $allocate->end_date,
                            $allocate->allocation,
                            $item,
                            $holiday
                        );

                        return $prev + $allocatedOfMonth / 100;
                    },
                    0
                );

                return $allocate;
            })
            ->reduce(fn ($prev, $item) => $prev + ($item->effort * $item->coefficient ?? 1), 0);
    }

    public function calCalenderEffortInStage($allocates, $holiday)
    {
        return $allocates
            ->map(function ($allocate) use ($holiday) {
                $from = Carbon::createFromFormat('Y-m-d', $allocate->start_date);
                $to = Carbon::createFromFormat('Y-m-d', $allocate->end_date);
                $allocate->man_day = $this->numberOfWorkingDays($from, $to, $holiday);

                $months = $this->resourceService->getMonthsBetween($from, $to);
                $allocate->effort = collect($months)->reduce(
                    function ($prev, $item) use ($allocate, $holiday) {
                        $allocatedOfMonth = $this->calManMonthSingleMonth(
                            $allocate->start_date,
                            $allocate->end_date,
                            $allocate->allocation,
                            $item,
                            $holiday
                        );

                        return $prev + $allocatedOfMonth / 100;
                    },
                    0
                );

                return $allocate;
            })
            ->reduce(fn ($prev, $item) => $prev + ($item->effort * $item->coefficient ?? 1), 0);
    }


    public function calActualEffort($allocates)
    {
        return $allocates->reduce(function ($prev, $item) {
            $currentDay = Carbon::now()->format('Y-m-d');
            $startDate = Carbon::createFromFormat('Y-m-d', $item->start_date);
            $endDate = Carbon::createFromFormat('Y-m-d', $item->end_date);
            if ($currentDay > $startDate && $currentDay < $endDate) {
                $actualWorkingDay = $startDate->diffInDays($currentDay);
                $daysInMonth = Carbon::now()->daysInMonth;
                $percentActualWorkingDay = $actualWorkingDay / 30 * 100;

                return $prev + ($percentActualWorkingDay * $item->allocation / 10000);
            }
        }, 0);
    }

    public function update($args, $id)
    {
        return $this->__updateProject($args, $id);
    }

    public function getProject($id, $args = [])
    {
        return $this->__getProject($id, $args);
    }

    public function getProjects($args = [])
    {
        return $this->__getProjects($args);
    }

    public function getMember($userRoles)
    {
        $userIds['user_ids'] = array_column($userRoles, 'user_id');
        $userIds['fields'] = ['user_id', 'name', 'code', 'division', 'position', 'skills', 'level', 'email'];

        return $this->__getUsers($userIds);
    }

    public function getMemberIdsByRoles($projectId, $role_names)
    {
        return $this->__getMemberIdsByRoles($projectId, $role_names);
    }

    public function addMember($projectId, $args)
    {
        return $this->addNewMember($projectId, $args);
    }

    public function removeMember($userId, $projectId, $id)
    {
        return $this->removeExistMember($userId, $projectId, $id);
    }

    public function calUserAllocatesOfProject($projectId): array
    {
        $allocates = $this->__getAllocationByProjectIds(['project_ids' => (array)$projectId]);
        $holidays = $this->getHoliday();

        return collect($allocates)
            ->groupBy('user_id')
            ->map(function ($userAllocates) use ($holidays) {
                return $userAllocates
                    ->map(
                        fn ($userAllocate) => $this->numberOfWorkingDays(
                            $userAllocate->start_date,
                            $userAllocate->end_date,
                            $holidays
                        ) * ($userAllocate->allocation / 100)
                    )
                    ->sum();
            })
            ->toArray();
    }

    public function show($id)
    {
        $project = $this->__showProject($id);
        if (!$project) {
            return null;
        }
        $pms = collect();
        $pqas = collect();
        $sellers = collect();
        $pmIds = $project->pm_ids ?? [];
        $pqaIds = $project->pqa_ids ?? [];
        $sellerIds = $project->seller_ids ?? [];
        $memberIds = array_column($project->user_role, 'user_id') ?? [];
        $userIds = array_merge($pmIds, $pqaIds, $sellerIds, $memberIds);
        $users = $this->__getUsers(['user_ids' => $userIds]);
        $roles = $this->__getRoles();
        $userAllocates = $this->calUserAllocatesOfProject($id);

        collect($users)
            ->each(function ($user) use (&$pms, &$pqas, &$sellers, $pmIds, $pqaIds, $sellerIds) {
                if (in_array($user->user_id, $pmIds)) {
                    $pms->push(
                        collect($user)->only(['user_id', 'name', 'email'])->all()
                    );
                }
                if (in_array($user->user_id, $pqaIds)) {
                    $pqas->push(
                        collect($user)->only(['user_id', 'name', 'email'])->all()
                    );
                }
                if (in_array($user->user_id, $sellerIds)) {
                    $sellers->push(
                        collect($user)->only(['user_id', 'name', 'email'])->all()
                    );
                }
            });
        $project->pms = $pms;
        $project->pqas = $pqas;
        $project->sellers = $sellers;
        /**
         * Roles outside the system and roles in the project are different
         */
        $members = array_filter($users, fn ($user) => in_array($user->user_id, $memberIds));
        $members = collect(array_values($members));
        $project = $this->mapMemberRoleNotPaginate($project, $members, $roles, $userAllocates);
        unset($project->user_role, $project->pm_ids, $project->pqa_ids, $project->seller_ids);

        return $project;
    }

    public function mapMemberRoleNotPaginate($project, $members, $roles, $userAllocates)
    {
        /**
         * push role_id to array key
         * map role name to array user role
         * search & return with correct member
         * return necessary information
         */
        $tmpRoles = [];
        foreach ($roles as $role) {
            $tmpRoles[$role->id] = $role;
        }
        $roles = $tmpRoles;
        $usersRole = $project->user_role;
        foreach ($usersRole as $key => $userRoleItem) {
            if (isset($roles[$userRoleItem->role_id])) {
                $usersRole[$key]->name = $roles[$userRoleItem->role_id]->name;
            }
        }
        $members = array_map(function ($userRole) use ($members, $userAllocates) {
            $member = $members->where('user_id', $userRole->user_id)->first();
            $position = $userRole;
            $userRoleID = $userRole->id ?? null;
            unset($position->id, $position->user_id, $position->project_id);
            if ($member) {
                return [
                    'user_id' => $member->user_id,
                    'name' => $member->name,
                    'code' => $member->code,
                    'email' => $member->email,
                    'avatar' => $member->avatar,
                    'position' => $member->position ?? [],
                    'allocate_days' => $userAllocates[$member->user_id] ?? 0,
                    'user_role' => $userRoleID,
                ];
            }
        }, $usersRole);
        $project->members = array_values(array_filter($members));

        return $project;
    }

    public function mapMemberRole($project, $members, $roles)
    {
        /**
         * push role_id to array key
         * map role name to array user role
         * search & return with correct member
         * return necessary information
         */
        $tmpRoles = [];
        foreach ($roles as $role) {
            $tmpRoles[$role->id] = $role;
        }
        $roles = $tmpRoles;
        $usersRole = $project->user_role->data;
        foreach ($usersRole as $key => $userRoleItem) {
            if (isset($roles[$userRoleItem->role_id])) {
                $usersRole[$key]->name = $roles[$userRoleItem->role_id]->name;
            }
        }
        $project->members = $project->user_role;
        $memberData = array_map(function ($userRole) use ($members) {
            $member = $members->where('user_id', $userRole->user_id)->first();
            if ($member != null) {
                $position['id'] = $userRole->role_id;
                $position['name'] = $userRole->name ?? '';
                $member->position = $position;
                unset($position->id, $position->user_id, $position->project_id);
                if (!$member) return [];

                return [
                    'user_id' => $member->user_id,
                    'code' => $member->code,
                    'name' => $member->name,
                    'email' => $member->email,
                    'position' => $member->position ?? [],
                    'level' => $member->level ?? [],
                    'skills' => $member->skills ?? [],
                    'division' => $member->division ?? [],
                    'user_role' => $userRole->id,
                    'position_in_project' => $userRole->position_in_project
                ];
            }
        }, $usersRole);
        $project->members->data = array_values(array_filter($memberData));

        return $project;
    }

    public function getWeeks($projectId)
    {
        return $this->__getWeeks($projectId);
    }

    public function getAllProject($args)
    {
        return $this->__getAllProject($args);
    }

    public function getOT($id, $accessToken)
    {
        $args['project_id'][] = $id;

        return $this->__getOT($args, $accessToken);
    }

    public function destroy($id)
    {
        return $this->__deleteProject(
            $id,
        );
    }

    public function getProjectType()
    {
        return $this->__getMasterData()->project_type;
    }

    public function getRequestProject($accessToken, $args)
    {
        $projects = $this->__getRequestProject($accessToken, $args);
        $userIds['user_ids'] = [];
        $divisionIds['division_ids'] = [];
        $customerIds['customer_ids'] = [];
        foreach ($projects->data as $project) {
            array_push($userIds['user_ids'], $project->pm_ids ?? null, $project->pqa_ids ?? null);
            array_push($divisionIds['division_ids'], $project->division_id ?? null);
            array_push($customerIds['customer_ids'], $project->customer_ids ?? null);
        }
        $query['user_ids'] = array_values(array_unique($userIds['user_ids']));
        $query['fields'] = ['user_id', 'name', 'code', 'avatar', 'email'];
        $divisionIds['division_ids'] = array_values(array_unique($divisionIds['division_ids']));
        $users = $this->__getUsers($query);
        $divisions = $this->__getDivisions($divisionIds);
        $customers = $this->__getCustomers($customerIds);
        $tmpProject = $projects->data;
        foreach ($tmpProject as $project) {
            $projectManager = array_filter($users, fn ($user) => $user->user_id == $project->pm_ids);
            $customerData = array_filter($customers, fn ($customer) => $customer->id == $project->customer_ids);
            $project->project_managers = array_values(
                array_map(fn ($user) => [
                    'id' => $user->user_id, 'name' => $user->name, 'avatar' => $user->avatar,
                ], $projectManager)
            );
            $project->customers = array_values(
                array_map(fn ($customers) => [
                    'id' => $customers->id, 'name' => $customers->name,
                ], $customerData)
            );
            $project->division = collect($divisions)->where('id', $project->division_id)->first();
            $project->budget = round((float) $project->budget, 2);
            $project->billable = round((float) $project->billable, 2);
        }
        $projects->data = $tmpProject;

        return $projects;
    }

    public function overview($id)
    {
        $project = $this->__overviewProject($id);
        $holiday = $this->getHoliday();
        $allocates = collect($project->allocations);
        $allocates->map(function ($allocate) {
            $now = Carbon::now();
            if ($now->lte($allocate->end_date)) {
                $allocate->end_date = $now->format('Y-m-d');
            }
        });

        // $budgetEffort = $this->calDailyReportEffort($project, $holiday);
        $budgetEffort = $this->calCalenderEffortWithCoefficient($allocates, $holiday);
        $expense['billable'] = [
            'total' => $project->billable,
            'used' => 0,
            'remaining' => $project->billable,
        ];
        $expense['budget'] = [
            'total' => $project->budget,
            'used' => $budgetEffort ?? 0,
            'remaining' => $project->budget - $budgetEffort ?? 0,
        ];
        if (!empty($project->stages)) {
            $stages = collect($project->stages);
            $allocateInStage = $stages->map(fn ($stage) => $stage->allocations)->flatten();
            // $billableEffort not using anymore
            $billableEffort = $this->calCalenderEffortWithCoefficient($allocateInStage, $holiday);
            $expense['billable'] = [
                'total' => $project->billable,
                'used' => $billableEffort ?? 0,
                'remaining' => $project->billable - $billableEffort ?? 0,
            ];
        }

        return $expense;
    }

    public function getListDeliverable($id)
    {
        return $this->__getListDeliverable($id);
    }

    public function getListMilestone($id)
    {
        return $this->__getListMilestone($id);
    }

    public function userBelongsToProject($userIds)
    {
        return $this->__userBelongsToProject($userIds);
    }

    public function getPmReports($args)
    {
        return $this->__getPmReports($args);
    }

    public function getOvertimeProject($args)
    {
        $overtimes = collect($this->__getOvertime($args));
        $totalOTOrigin = $overtimes->sum('ot');
        $totalOTCoefficient = $overtimes->sum('ot_with_coefficient');

        return [
            'total_ot_origin' => $totalOTOrigin,
            'total_ot_coefficient' => $totalOTCoefficient,
        ];
    }

    public function requestOpenProjectDl($args)
    {
        return $this->__requestOpenProjectDl($args);
    }

    public function getDataProject($args)
    {
        $projects = $this->getProjects($args);
        if (!isset($args['disable_paginate'])) $projects = $projects['data'];

        return collect($projects)->map(function ($project) {
            return collect($project)
                ->only(['id', 'name', 'code', 'customers'])
                ->all();
        });
    }

    public function getListMemberInProject($id)
    {
        $members = $this->__getMembers($id, [
            'pagination' => false
        ]);
        $userIds = array_column($members, 'user_id');
        $users = $this->__getUsers([
            'user_ids' => $userIds
        ]);

        return collect($users)->map(function ($user) {
            return collect($user)
                ->only(['user_id', 'name', 'email', 'avatar', 'position'])
                ->all();
        });
    }

    public function getListHoliday($args = [])
    {
        return $this->__getListHoliday($args);
    }

    public function getProjectsOfUser($args = [])
    {
        return $this->__getProjectsOfUser($args);
    }

    public function getProjectBudget($args, $id)
    {
        return $this->__getProjectBudget($args, $id);
    }

    public function updateProjectBudget($args, $id)
    {
        return $this->__updateProjectBudget($args, $id);
    }

    public function getEEChartData($args, $id)
    {
        $fromMonth = $args['from_month'];
        $toMonth = $args['to_month'];
        $projectServiceData = $this->__getEEChartData([
            'from_month' => $fromMonth,
            'to_month' => $toMonth
        ], $id);
        $startDate = Carbon::parse($fromMonth)->startOfMonth()->format('Y-m-d');
        $endDate = Carbon::parse($toMonth)->endOfMonth()->format('Y-m-d');
        $months = collect($this->generateMonthList($fromMonth, $toMonth))->mapWithKeys(function ($item, $key) {
            return [$item => $item];
        });
        $holiday = $this->getListHoliday();
        $allocations = $projectServiceData->allocations;
        $dailyReports = $projectServiceData->daily_reports;
        $budget = $projectServiceData->budget;

        $userIds = collect($dailyReports)->pluck('user_id')->concat(collect($allocations)->pluck('user_id'))->unique()->toArray();
        $resources = $this->resourceService->getResources(['start_date' => $startDate, 'end_date' => $endDate, 'user_ids' => $userIds]);
        $unpaidLeaves = $this->__getUserUnpaidLeaveByTime(['from' => $startDate, 'to' => $endDate]);

        $monthBudget = $this->calculateBudgetByMonth($budget->month_budget, $months);
        $calendarEffort = $this->calculateCalendarEffortByMonth($resources, $allocations, $months, $holiday, $unpaidLeaves);
        $actualEffort = $this->calculateActualEffortByMonth($resources, $dailyReports, $allocations, $months, $holiday, $unpaidLeaves);

        $eeCalendarEffort = collect($months)->map(function ($month) use ($monthBudget, $calendarEffort) {
            return ($calendarEffort[$month] === 0.0) ? 0 : $monthBudget[$month] / round($calendarEffort[$month], 2);
        })->toArray();
        $eeActualEffort = collect($months)->map(function ($month) use ($monthBudget, $actualEffort) {
            return ($actualEffort[$month] === 0.0) ? 0 : $monthBudget[$month] / round($actualEffort[$month], 2);
        })->toArray();
        $monthBudgetNodes = $this->collectEEChartColumnNodes($monthBudget, 'budget');
        $calendarEffortNodes = $this->collectEEChartColumnNodes($calendarEffort, 'calendar_effort');
        $actualEffortNodes = $this->collectEEChartColumnNodes($actualEffort, 'actual_effort');
        $eeCalendarEffortNodes = $this->collectEEChartLineNodes($eeCalendarEffort, 'ee_calendar_effort');
        $eeActualEffortNodes = $this->collectEEChartLineNodes($eeActualEffort, 'ee_actual_effort');

        return [
            'months' => $months->values(),
            'month_budget' => $monthBudgetNodes,
            'calendar_effort' => $calendarEffortNodes,
            'actual_effort' => $actualEffortNodes,
            'ee_calendar_effort' => $eeCalendarEffortNodes,
            'ee_actual_effort' => $eeActualEffortNodes
        ];
    }

    function collectEEChartColumnNodes($data, $type)
    {
        return collect($data)->map(function ($value, $month) use ($type) {
            return new EEChartColumnNode(
                $type,
                formatDate($month, 'm/Y'),
                round($value, 2),
                $type
            );
        })->values()->toArray();
    }

    function collectEEChartLineNodes($data, $name)
    {
        return collect($data)->map(function ($value, $month) use ($name) {
            return new EEChartLineNode(
                $name,
                formatDate($month, 'm/Y'),
                round($value, 2)
            );
        })->values()->toArray();
    }

    // $budget: month_budget get from API Project service
    // $months: array['YYYY-MM']; ex: ['2023-01', '2023-02', '2023-03']
    // output: array['YYYY-MM' => number]; ex: ['2023-01' => 10, '2023-02' => 11]
    public function calculateBudgetByMonth($budget, $months)
    {
        $budgets = collect($budget)->keyBy('month')->toArray();
        $budgetByMonth = collect($months)->map(function ($month) use ($budgets) {
            return $budgets[$month]->budget ?? 0;
        })->toArray();
        return $budgetByMonth;
    }

    // $allocations: array
    // $months: array['YYYY-MM']; ex: ['2023-01', '2023-02', '2023-03']
    // $holiday: array get from HR API
    // output: array['YYYY-MM' => number]; ex: ['2023-01' => 10, '2023-02' => 11]
    public function calculateCalendarEffortByMonth($resources, $allocations, $months, $holiday, $unpaidLeaves, $applyCoefficient = true)
    {
        $unpaidLeavesCollection = collect($unpaidLeaves);
        $resourcesCollection = collect($resources);
        $effort = collect($months)
            ->map(function ($month) use ($resourcesCollection, $allocations, $holiday, $unpaidLeavesCollection, $applyCoefficient) {
                return $this->calMonthEffortUsingAllocation($month, $resourcesCollection, $allocations, $holiday, $unpaidLeavesCollection, false, $applyCoefficient);
            })->toArray();
        return $effort;
    }

    private function calMonthEffortUsingAllocation($month, $resourcesCollection, $allocations, $holiday, $unpaidLeavesCollection, $isCurrentMonth = false, $applyCoefficient = true)
    {
        $startDate = Carbon::createFromFormat('Y-m-d', $month . '-01')->startOfMonth()->format('Y-m-d');
        $endDate = Carbon::createFromFormat('Y-m-d', $month . '-01')->endOfMonth()->format('Y-m-d');
        $numberWorkingHoursInMonth = $this->getNumberOfWorkingHours(
            $startDate,
            $endDate,
            $holiday
        );

        $monthEffort = collect($allocations)->reduce(function ($carry, $allocationRecord)
        use ($startDate, $endDate, $month, $holiday, $resourcesCollection, $unpaidLeavesCollection, $numberWorkingHoursInMonth, $isCurrentMonth, $applyCoefficient) {
            $userId = $allocationRecord->user_id;
            $coefficient = $applyCoefficient ? $allocationRecord->coefficient : 1;
            $userUnpaidLeaves = $unpaidLeavesCollection->filter(fn ($item) => $item->user_id === $userId);
            $userResource = $resourcesCollection->first(function ($resource) use ($userId) {
                return $resource->user_id === $userId;
            });

            $numberOfAllocationHours = $this->calNumberOfAllocationHours(
                $allocationRecord,
                $userResource,
                $isCurrentMonth ? Carbon::now()->format('Y-m-d') : $startDate,
                $endDate,
                $holiday,
                $userUnpaidLeaves
            );

            return $carry + (
                $coefficient *
                $numberOfAllocationHours *
                $allocationRecord->allocation
            ) / ($numberWorkingHoursInMonth * 100);
        }, 0.0);
        return $monthEffort;
    }

    private function calNumberOfAllocationHours($allocationRecord, $resource, $startDate, $endDate, $holiday, $userUnpaidLeaves)
    {
        if (!$resource) {
            return 0;
        }
        $startWorkDate = $resource->onboard_date ?? $allocationRecord->start_date;
        $endWorkDate = $resource->checkout_date ?? $allocationRecord->end_date;
        $numberOfAllocationHours = $this->getNumberOfWorkingHours(
            max($startDate, $allocationRecord->start_date, $startWorkDate),
            min($endDate, $allocationRecord->end_date, $endWorkDate),
            $holiday,
            $userUnpaidLeaves,
            $resource->onboard_date ?? null,
            $resource->checkout_date ?? null
        );

        return $numberOfAllocationHours;
    }

    // $dailyReports: array
    // $allocations: array
    // $months: array['YYYY-MM']; ex: ['2023-01', '2023-02', '2023-03']
    // $holiday: array get from HR API
    // output: array['YYYY-MM' => number]; ex: ['2023-01' => 10, '2023-02' => 11]
    public function calculateActualEffortByMonth($resources, $dailyReports, $allocations, $months, $holiday, $unpaidLeaves)
    {
        $unpaidLeavesCollection = collect($unpaidLeaves);
        $resourcesCollection = collect($resources);
        $currentTime = Carbon::now();
        $currentMonth = $currentTime->format('Y-m');
        $currentDate = $currentTime->format('Y-m-d');
        $today = Carbon::now()->format('Y-m-d');
        $startOfCurrentMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $endOfCurrentMonth = Carbon::now()->endOfMonth()->format('Y-m-d');

        $effort = [];
        $monthsInPast = [];
        $monthsInFuture = [];
        foreach ($months as $month) {
            if ($month < $currentMonth) {
                $monthsInPast[$month] = $month;
            } elseif ($month > $currentMonth) {
                $monthsInFuture[$month] = $month;
            }
        }

        $effortInPast = collect($monthsInPast)->map(function ($month) use ($resourcesCollection, $allocations, $dailyReports, $holiday, $unpaidLeavesCollection) {
            $v2ApplyDate = config('app.v2_apply_date');
            if ($month <= Carbon::createFromFormat('Y-m-d', $v2ApplyDate)->subMonth()->format('Y-m')) {
                return $this->calMonthEffortUsingAllocation($month, $resourcesCollection, $allocations, $holiday, $unpaidLeavesCollection);
            } else {
                return $this->calMonthEffortUsingDailyReport($dailyReports, $month, $holiday);
            }
        })->toArray();

        $effortCurrentMonth = [];
        if (collect($months)->contains($currentMonth)) {
            $dailyReportsExceptToday = array_filter($dailyReports, function ($report) use ($today) {
                return $report->work_date !== $today;
            });
            $reportedEffortThisMonth = $this->calMonthEffortUsingDailyReport($dailyReportsExceptToday, $currentMonth, $holiday);
            $remainCalendarEffortThisMonth = $this->calMonthEffortUsingAllocation($currentMonth, $resourcesCollection, $allocations, $holiday, $unpaidLeavesCollection, true);
            $effortCurrentMonth[$currentMonth] = $reportedEffortThisMonth + $remainCalendarEffortThisMonth;
        }

        $effortInFuture = $this->calculateCalendarEffortByMonth($resources, $allocations, $monthsInFuture, $holiday, $unpaidLeaves);
        return $effortInPast + $effortCurrentMonth + $effortInFuture;
    }

    public function calMonthEffortUsingDailyReport($dailyReports, $month, $holiday)
    {
        $startMonthDate = Carbon::parse($month)->startOfMonth()->format('Y-m-d');
        $endMonthDate = Carbon::parse($month)->endOfMonth()->format('Y-m-d');
        $numOfWorkingDays = $this->getNumberOfWorkingDays($startMonthDate, $endMonthDate, $holiday);
        $sumActualTime = $this->calculateActualTimeInRange($dailyReports, $startMonthDate, $endMonthDate);
        return floatval($sumActualTime / ($numOfWorkingDays * EBase::WORKING_DAY_HOURS));
    }

    // sum actual time of daily reports in time range
    // return (hour * coefficient)
    function calculateActualTimeInRange($dailyReports, $fromDate, $toDate)
    {
        return collect($dailyReports)->reduce(function ($sum, $item) use ($fromDate, $toDate) {
            $effort = ($fromDate <= $item->work_date && $item->work_date <= $toDate) ? $item->actual_time : 0;
            return $sum + ($effort * ($item->coefficient ?? 1));
        }, 0);
    }

    public function getProjectMembers($args)
    {
        return $this->__getProjectMembers($args);
    }

    public function getProjectStatistics($args)
    {
        $user = $this->getCurrentUserInfo();
        $args['division_id'] = $user->division->division_id;
        $args['position_id'] = $user->position->id;

        $firstOfMonth = Carbon::parse($args['month'])->firstOfMonth()->toDateString();
        $lastOfMonth = Carbon::parse($args['month'])->lastOfMonth()->toDateString();
        $holidays = $this->getHoliday();
        $unpaidLeaves = collect($this->__getListUserUnpaidLeave())->groupBy('user_id')->toArray();
        $users = $this->userService->getUsersWithFilter()->keyBy('user_id')->toArray();
        $divisions = collect($this->__getDivisions())->pluck('name', 'id')->toArray();
        $projects = $this->__getProjectStatistics($args);

        $projects->data = collect($projects->data)->map(function ($project) use (
            $users,
            $divisions,
            $firstOfMonth,
            $lastOfMonth,
            $holidays,
            $unpaidLeaves
        ) {
            $project->pm_names = array_map(function ($pmId) use ($users) {
                return $users[$pmId]['name'] ?? '';
            }, $project->pm_ids ?? []);

            $project->division_name = $divisions[$project->division_id] ?? '';

            $userAllocates = collect($project->allocates)->groupBy('user_id');
            $userDailyReports = collect($project->daily_reports)->groupBy('user_id');

            // calculate calendar effort of project from start of month to end of month
            $calendarEffort = $this->processCalendarEffort(
                $userAllocates,
                $firstOfMonth,
                $lastOfMonth,
                $holidays,
                $unpaidLeaves,
                $users
            );

            $project->calendar_effort = round($calendarEffort, 2);

            // calculate actual effort of project from start of month to now + calendar effort of project from now to end of month
            $actualEffort = $this->processActualEffort(
                $userDailyReports,
                $firstOfMonth,
                $lastOfMonth,
                $holidays,
                $unpaidLeaves,
                $userAllocates
            );

            $project->actual_effort = round($actualEffort, 2);

            unset($project->daily_reports, $project->allocates);

            return $project;
        });

        return $projects;
    }

    protected function processActualEffort(
        $userDailyReports,
        $firstOfMonth,
        $lastOfMonth,
        $holidays,
        $unpaidLeaves,
        $userAllocates,
    ) {
        $today = Carbon::today();

        $allocateUserIds = collect($userAllocates)->keys()->toArray();
        $dailyReportUserIds = collect($userDailyReports)->keys()->toArray();
        $userIds = array_unique(array_merge($allocateUserIds, $dailyReportUserIds));
        $sumActualEffort = 0;

        foreach ($userIds as $userId) {
            $unpaidLeave = $unpaidLeaves[$userId] ?? [];
            $allocates = $userAllocates[$userId] ?? [];
            $dailyReports = $userDailyReports[$userId] ?? [];

            $workingHours = $this->getNumberOfWorkingDays(
                $firstOfMonth,
                $lastOfMonth,
                $holidays,
                $unpaidLeave,
                $user['onboard_date'] ?? null,
                $user['checkout_date'] ?? null
            ) * 8;

            if ($workingHours == 0) {
                continue;
            }

            $calendarHour = $this->resourceService->calAllocateEffortWithCoefficient(
                collect($allocates),
                $today->gt($firstOfMonth) ? $today->toDateString() : $firstOfMonth,
                $lastOfMonth,
                $holidays,
                $unpaidLeave
            );

            // calculate actual effort of user in this month (exclude today)
            $actualHour = $this->resourceService->calLogWorkEffortWithConfficient($dailyReports);

            $sumActualEffort += ($actualHour + $calendarHour) / $workingHours;
        }

        return $sumActualEffort;
    }

    protected function processCalendarEffort(
        $userAllocates,
        $firstOfMonth,
        $lastOfMonth,
        $holidays,
        $unpaidLeaves,
        $users
    ) {
        return $userAllocates->map(
            function ($allocates, $userId) use (
                $firstOfMonth,
                $lastOfMonth,
                $holidays,
                $unpaidLeaves,
                $users
            ) {
                $user = $users[$userId] ?? [];
                $unpaidLeave = $unpaidLeaves[$userId] ?? [];

                $workingHours = $this->getNumberOfWorkingDays(
                    $firstOfMonth,
                    $lastOfMonth,
                    $holidays,
                    $unpaidLeave,
                    $user['onboard_date'] ?? null,
                    $user['checkout_date'] ?? null
                ) * 8;

                if ($workingHours == 0) {
                    return 0;
                }

                $calendarEffortHour = $this->resourceService->calAllocateEffortWithCoefficient(
                    $allocates,
                    $firstOfMonth,
                    $lastOfMonth,
                    $holidays,
                    $unpaidLeave,
                );

                return $calendarEffortHour / $workingHours;
            }
        )
            ->sum();
    }

    public function getProjectBudgetStatistics($args)
    {
        $year = $args['year'] ?? date('Y');
        $from = Carbon::createFromDate($year)->firstOfYear()->format('Y-m-d');
        $to = Carbon::createFromDate($year)->lastOfYear()->format('Y-m-d');
        $holiday = $this->getHoliday();
        $unpaidLeaves = $this->__getUserUnpaidLeaveByTime(['from' => $from, 'to' => $to]);
        $args['project_types'] = [EProjectType::BY_CUSTOMER, EProjectType::OPPORTUNITY, EProjectType::TRAINING];
        $applyCoefficient = false;
        $projectBudgets = $this->__getProjectBudgets($args);
        if (isset($projectBudgets->year_budget)) $projectIds = array_column($projectBudgets->year_budget, 'project_id');
        $months = collect($this->generateMonthList($from, $to))->mapWithKeys(function ($item, $key) {
            return [$item => $item];
        });
        $paramsGetAllocation = [
            'project_ids' => $projectIds ?? null,
            'from' => $from,
            'to' => $to,
        ];
        $allocations = $this->__getAllocationByProjectIds($paramsGetAllocation);
        $allocations = collect($allocations);
        $userIds = $allocations->pluck('user_id')->unique();

        $users = $this->userService->getUsers([
            'user_ids' => $userIds->toArray(),
            'fields' => ['user_id', 'name', 'onboard_date', 'checkout_date', 'status']
        ]);

        $divisions = collect($this->__getDivisions())->pluck('name', 'id')->toArray();
        $yearBudgets = collect($projectBudgets->year_budget)->keyBy('project_id')->toArray();
        return collect($projectBudgets->month_budget)
            ->groupBy('project_id')
            ->map(function ($projectBudget, $projectId) use ($yearBudgets, $divisions, $allocations, $users, $months, $holiday, $unpaidLeaves, $applyCoefficient) {
                $allocations = $allocations->where('project.id', $projectId)->toArray();
                $calendarEffort = $this->calculateCalendarEffortByMonth($users, $allocations, $months, $holiday, $unpaidLeaves, $applyCoefficient);
                return [
                    'id' => $projectId,
                    'project_id' => $projectId ?? null,
                    'project_name' => $projectBudget->first()->project->name ?? null,
                    'project_type' => $projectBudget->first()->project->project_type ?? null,
                    'division_name' => $divisions[$projectBudget->first()->project->division_id ?? null] ?? '',
                    'year_budget' => $yearBudgets[$projectId]->budget ?? null,
                    'month_budget' => $projectBudget->map(function ($monthBudget) {
                        return [
                            'month' => $monthBudget->month,
                            'budget' => $monthBudget->budget,
                        ];
                    })->toArray(),
                    'calendar_effort' => collect($calendarEffort)
                        ->map(function ($effort, $month) {
                            return [
                                'month' => $month,
                                'effort' => $effort,
                            ];
                        })
                        ->filter(fn ($calendarEffort) => $calendarEffort['effort'] != 0)
                        ->values()
                        ->toArray(),
                ];
            })
            ->values();
    }

    public function exportProject()
    {
        $projects = $this->__getProjects([
            'project_type' => [EProjectType::BY_CUSTOMER],
            'disable_paginate' => 1,
            'relation' => ['pms', 'customers'],
        ]);
        $projects = collect($projects);
        $users = $this->__getUsers();
        $users = collect($users);
        $divisions = $this->__getDivisions();
        $divisions = collect($divisions);
        $team = $this->__getTeams();
        $team = collect($team);

        $projects = $projects->map(function ($project) use ($users, $divisions, $team) {
            $project = (array) $project;
            $pmIds = array_column($project['pms'], 'user_id');
            return [
                'code' => $project['code'],
                'name' => $project['name'],
                'division' => $divisions->where('id', $project['division_id'])->first()->name ?? '',
                'team' => $team->where('id', $project['team_id'])->first()->name ?? '',
                'project_managers' => $users->filter(fn ($user) => in_array($user->user_id, $pmIds))
                    ->map(fn ($user) => $user->name)->implode(', '),
                'project_type' => EProjectType::getActionString($project['project_type']),
                'start_date' => $project['start_date'],
                'end_date' => $project['end_date'],
                'billable' => $project['billable'],
                'status' => EProjectType::getStatus($project['status']),
                'industry' => EProjectType::getIndustry($project['industry']),
                'customers' => collect(($project['customers']))->pluck('name')->implode(', '),
                'language' => $project['language'],
                'scope' => $project['scope'],
                'description' => $project['description']
            ];
        })->toArray();
        return $projects;
    }

    public function getDailyReportStatistic($userId, $args)
    {
        $worklogs = [];
        $month = @$args['month'] ?: date('Y-m');
        $projects = $this->__getProjectsOfUserRolePM([
            'user_id' => $userId,
            'month' => $month,
        ]);
        if (empty($projects)) return $worklogs;
        $projectIds = array_unique(array_column($projects, 'id'));
        $userIds = collect($this->__getUsers([
            'user_ids' => array_unique(array_column($projects, 'user_id')),
            'fields' => ['user_id', 'name', 'email', 'avatar']
        ]))->keyBy('user_id')->toArray();
        $allocations = $this->__getAllocationByProjectIds([
            'project_ids' => $projectIds,
            'from' => date("Y-m-01", strtotime($month)),
            'to' => date("Y-m-t", strtotime($month)),
        ]);
        $allocations = collect($allocations)->groupBy('project.id')->map(fn ($group) => collect($group)->groupBy('user_id'))->toArray();
        foreach ($projects as $key => $project) {
            $expected_time = 0;
            if (@$allocations[$project->id][$project->user_id]) {
                $allocation = $allocations[$project->id][$project->user_id];
                foreach ($allocation as $key => $value) {
                    if ($project->work_date >= $value->start_date && $project->work_date <= $value->end_date) {
                        $expected_time = $value->allocation / 100 * 8;
                        break;
                    }
                }
            }
            if (!isset($worklogs[$project->id])) {
                $worklogs[$project->id] = [
                    'id' => $project->id,
                    'name' => $project->name,
                ];
            }
            if (!isset($worklogs[$project->id]['users'][$project->user_id])) {
                $worklogs[$project->id]['users'][$project->user_id] = [
                    'id' => $project->user_id,
                    'name' => $userIds[$project->user_id]->name,
                    'email' => $userIds[$project->user_id]->email,
                    'avatar' => $userIds[$project->user_id]->avatar,
                ];
            }
            $worklogs[$project->id]['users'][$project->user_id]['daily_reports'][$project->work_date] = [
                'work_date' => $project->work_date,
                'actual_time' => $project->actual_time,
                'expected_time' => $expected_time,
            ];
            if (@$worklogs[$project->id]['daily_reports'][$project->work_date]) {
                $worklogs[$project->id]['daily_reports'][$project->work_date]['actual_time'] += $project->actual_time;
                $worklogs[$project->id]['daily_reports'][$project->work_date]['expected_time'] += $expected_time;
            } else {
                $worklogs[$project->id]['daily_reports'][$project->work_date] = [
                    'work_date' => $project->work_date,
                    'actual_time' => $project->actual_time,
                    'expected_time' => $expected_time,
                ];
            }
        }
        foreach ($worklogs as $worklog) {
            foreach ($worklog['daily_reports'] as $key => $value) {
                $worklogs[$project->id]['daily_reports'][$key]['percent'] = $value['actual_time'] / ($value['expected_time'] ?: 1) * 100;
            }
        }
        // convert object data to array of values
        $worklogs = collect($worklogs)->values()->map(function ($project) {
            $project['daily_reports'] = collect($project['daily_reports'])->values();
            $project['users'] = collect($project['users'])->values()->map(function ($user) {
                $user['daily_reports'] = collect($user['daily_reports'])->values();
                return $user;
            });
            return $project;
        });

        return $worklogs;
    }

    public function exportAllocationStatistics($allocations, $months, $unpaidLeaves)
    {
        $holiday = $this->getHoliday();
        $applyCoefficient = false;
        $userIds = $allocations->pluck('user_id')->unique();

        $users = $this->userService->getUsers([
            'user_ids' => $userIds->toArray(),
            'fields' => ['user_id', 'name', 'onboard_date', 'checkout_date', 'status']
        ]);
        $calendarEffort = [];
        collect($users)
            ->each(function ($user) use ($allocations, $months, $holiday, $unpaidLeaves, $applyCoefficient, &$calendarEffort) {
                $userAllocates = $allocations->where('user_id', $user->user_id)->toArray();
                $monthEffort = $this->calculateCalendarEffortByMonth([$user], $userAllocates, $months, $holiday, $unpaidLeaves, $applyCoefficient);
                $calendarEffort[] = [
                    'user_id' => $user->user_id,
                    'name' => $user->name,
                    'calendar_effort' => $monthEffort,
                ];
            });
        return $calendarEffort;
    }

    public function exportLogWorkStatistics($dailyReports, $months)
    {
        $holiday = $this->getHoliday();
        $userIds = $dailyReports->pluck('user_id')->unique();

        $users = $this->userService->getUsers([
            'user_ids' => $userIds->toArray(),
            'fields' => ['user_id', 'name', 'onboard_date', 'checkout_date', 'status']
        ]);
        $calendarEffort = [];
        collect($users)
            ->each(function ($user) use ($dailyReports, $months, $holiday, &$calendarEffort) {
                $monthEffort = [];
                foreach ($months as $month) {
                    $tmpMonth = Carbon::parse($month);
                    $userDailyReports = $dailyReports->filter(
                        fn ($report) => $report->user_id == $user->user_id  && $tmpMonth->isSameMonth($report->work_date)
                    )->toArray();

                    $monthEffort[$month] = (empty($userDailyReports))
                        ? 0
                        : $this->calMonthEffortUsingDailyReport($userDailyReports, $month, $holiday);
                }
                $calendarEffort[] = [
                    'user_id' => $user->user_id,
                    'name' => $user->name,
                    'calendar_effort' => $monthEffort,
                ];
            });
        return $calendarEffort;
    }

    public function getProjectLogWork($date)
    {
        $projects = $this->__getListProjects(
            ['fields' => ['id', 'name', 'teams_webhook_url'], 'disable_paginate' => 1, 'is_teams_webhook_url' => true]
        );
        $projectIds = array_column($projects, 'id');
        $paramsGetAllocation = [
            'project_ids' => $projectIds ?? [],
            'from' => $date,
            'to' => $date
        ];
        $allocations = $this->allocationService->getAllocationByProjectIds($paramsGetAllocation);
        $allocations = collect($allocations);
        $paramsGetReport = [
            'project_id' => $projectIds ?? [],
            'from_date' => $date,
            'to_date' => $date,
            'status' => [EDailyReport::SUCCESS_STATUS, EDailyReport::PENDING_STATUS],
            'columns' => ['user_id', 'project_id', 'coefficient', 'work_date', 'actual_time', 'status'],
            'get_project' => 0,
        ];
        $dailyReports = $this->dailyReportService->__getListReportOfUser($paramsGetReport);
        $dailyReports = collect($dailyReports);
        $activeProjectIds = $allocations
            ->map(fn($item) => $item->project->id)
            ->unique()->toArray();
        $projects = collect($projects)
            ->filter(function ($item) use ($activeProjectIds) {
                return in_array($item->id, $activeProjectIds);
            })
            ->values();
        $userIds = $allocations->pluck('user_id')->unique();
        $users = $this->userService->getUsers(
            ['user_ids' => $userIds, 'fields' => ['user_id', 'name', 'email']]
        );
        $users = collect($users)->keyBy('user_id')->toArray();

        $projects->transform(function ($project) use ($users, $dailyReports, $allocations) {
            $allocates = $allocations->filter(fn ($item) => @$item->project->id == $project->id);
            $dailyReports = $dailyReports->filter(fn ($item) => @$item->project_id == $project->id);
            $dailyReports = $dailyReports->groupBy('user_id')->map(fn($item) => $item->sum('actual_time'))->toArray();
            $project->users = $allocates->groupBy('user_id')->map(function ($allocate) use ($users, $dailyReports) {
                $userId = $allocate->first()->user_id;
                $logWork = $dailyReports[$userId] ?? 0;
                $allocation = $allocate->sum('allocation') / 100 * 8;
                if ($logWork < $allocation) {
                    return [
                        'user_id' => $userId,
                        'name' => $users[$userId]->name,
                        'email' => $users[$userId]->email,
                        'allocation' => $allocation,
                        'log_work' => $logWork
                    ];
                }
            })->filter()->toArray();

            return $project;
        });

        return $projects->toArray();
    }

    public function getPerformanceMember($args)
    {
        $args['pagination'] = false;
        $args['fields'] = ['id', 'name'];
        $month = $args['month'] ?? date('Y-m');
        $projects = $this->__getListProject($args);
        if ($projects) {
            $holiday = $this->resourceService->getHoliday();
            $projectIds = collect($projects)->pluck('id')->toArray();
            $users = $this->__getUsers(['fields' => ['user_id', 'name', 'avatar']]);
            $users = collect($users)->keyBy('user_id')->toArray();
            $from = $month . '-01';
            $to = date("Y-m-t", strtotime($from));
            $allocations = $this->__getAllocationByProjectIds([
                'project_ids' => $projectIds,
                'from' => $from,
                'to' => $to,
            ]);
            $allocations = collect($allocations)->groupBy('project.id')->map(fn ($allocation) => $allocation->groupBy('user_id'))->toArray();
            $tasks = $this->__getListTask([
                'fields' => ['project_id', 'user_id', 'estimate_hours', 'actual_hours', 'billable'],
                'project_ids' => $projectIds,
                'month' => $month,
            ]);
            $tasks = collect($tasks)->groupBy("project_id")->map(function ($task) {
                $task = $task->groupBy('user_id')->map(function ($data) {
                    return [
                        'estimate_hours' => $data->sum('estimate_hours'),
                        'actual_hours' => $data->sum('actual_hours'),
                        'billable' => $data->sum('billable'),
                    ];
                });
                return $task;
            })->toArray();

            $projects = collect($projects)->map(function ($project) use ($allocations, $tasks, $users, $from, $to, $holiday) {
                $array = [];
                $projectId = $project->id;
                $project = [
                    'id' => $projectId,
                    'name' => $project->name,
                ];
                if (@$allocations[$projectId]) {
                    foreach ($allocations[$projectId] as $key => $allocation) {
                        $totalAllocation = 0;
                        foreach ($allocation as $value) {
                            $actualWorkingDays = $this->getNumberOfWorkingDays($value->start_date, $value->end_date, $holiday, [], null, null);
                            $totalAllocation += $actualWorkingDays * ($value->allocation / 100) * 8;
                        }
                        $user = $users[$key];
                        $array[$key] = [
                            'id' => $key,
                            'name' => $user->name,
                            'avatar' => $user->avatar,
                            'allocation' => $totalAllocation,
                        ];
                    }
                }
                if (@$tasks[$projectId]) {
                    foreach ($tasks[$projectId] as $key => $value) {
                        $user = $users[$key];
                        if (!isset($array[$key])) {
                            $array[$key] = [
                                'id' => $key,
                                'name' => $user->name,
                                'avatar' => $user->avatar,
                            ];
                        }
                        $array[$key] += [
                            're_estimate' => $value['estimate_hours'],
                            'actual_effort' => $value['actual_hours'],
                            'billable' => $value['billable'],
                        ];
                    }
                }
                foreach ($array as $key => $value) {
                    $array[$key] = $this->performance($array[$key], 're_estimate_perf', 're_estimate', 'allocation');
                    $array[$key] = $this->performance($array[$key], 'billable_perf', 'billable', 'allocation');
                }
                $project = $this->sumColumn($project, $array, 'allocation');
                $project = $this->sumColumn($project, $array, 're_estimate');
                $project = $this->sumColumn($project, $array, 'actual_effort');
                $project = $this->sumColumn($project, $array, 'billable');
                $project = $this->performance($project, 're_estimate_perf', 're_estimate', 'allocation');
                $project = $this->performance($project, 'billable_perf', 'billable', 'allocation');
                $project['users'] = array_values($array);

                return $project;
            });
        }

        return $projects;
    }

    /**
     * Safely divides two numbers, returning 0 if the divisor is 0
     *
     * @param float $numerator
     * @param float $denominator
     * @return float
     */
    private function safeDivide($numerator, $denominator)
    {
        return $denominator !== 0 ? $numerator / $denominator : 0;
    }

    /**
     * Calculates working hours in a month
     *
     * @return float
     */
    private function getWorkingHoursInMonth()
    {
        return EDailyReport::WORKING_HOURS_IN_DAY * EDailyReport::WORKING_DAYS_IN_MONTH;
    }

    /**
     * Get project performance data
     *
     * @param array $args
     * @return \Illuminate\Support\Collection
     */
    public function getProjectPerformance($args)
    {
        $projectId = $args['project_id'];
        $sprints = $this->__getProjectPerformance($projectId)?->data;
        $overtimes = collect($this->__getOvertime([
            'project_ids' => [$projectId],
        ]));

        return collect($sprints)->map(function ($sprint) use ($overtimes) {
            return $this->processSprintPerformance($sprint, $overtimes);
        });
    }

    public function importProjectPerformance($projectId, $data)
    {
        return $this->__importProjectPerformance($projectId, $data);
    }

    public function getDataImportProjectPerformance($file, $projectId)
    {
        $data = Excel::toCollection(new ProjectPerformanceImport, $file);
        $masterData = $data->get(EProjectPerformanceImport::MASTER_DATA_SHEET);
        $functionsData = $data->get(EProjectPerformanceImport::FUNCTION_SHEET);
        $functions = $this->generateFunctionRecords($functionsData);

        return collect([
            'function_categories' => $this->getFunctionCategoriesFromFunctions($functions),
            'sprints' => $this->generatePerformanceData(
                $masterData,
                $functions,
                $projectId,
            ),
        ]);
    }

    private function generatePerformanceData($masterData, $functions, $projectId)
    {
        return $masterData?->map(function (Collection $row) use ($functions, $projectId) {
            $sprint = $this->generateRecord(
                EProjectPerformanceImport::MASTER_DATA_SHEET,
                EProjectPerformanceImport::SPRINT_COLUMNS,
                $row
            );

            $functionsOfSprint = $this->getFunctionOfSprint($functions, $sprint);

            $defects = $this->generateDefectRecords($row);

            $qualityGate = $this->generateRecord(
                EProjectPerformanceImport::MASTER_DATA_SHEET,
                EProjectPerformanceImport::QUALITY_GATE_COLUMNS,
                $row
            );

            return $sprint->merge([
                'project_id' => $projectId,
                'functions' => $functionsOfSprint,
                'defects' => $defects,
                'quality_gate' => $qualityGate,
            ]);
        });
    }

    private function getFunctionOfSprint($functions, $sprint)
    {
        return $functions
            ->where('sprint', $sprint->get('name'))
            ->map(function ($function) {
                return $function->except('sprint');
            });
    }

    private function getFunctionCategoriesFromFunctions($functions) {
        return $functions->pluck('function_category')->unique(function ($value) {
            return trim($value);;
        });
    }

    private function generateFunctionRecords($dataInFunctionSheet)
    {
        return $dataInFunctionSheet?->map(function (Collection $row) {
            return $this->generateRecord(
                EProjectPerformanceImport::FUNCTION_SHEET,
                EProjectPerformanceImport::FUNCTION_COLUMNS,
                $row,
            );
        });
    }

    private function generateRecord($sheet, $columns, $row)
    {
        return collect($columns)->mapWithKeys(function ($column) use ($row, $sheet) {
            return EProjectPerformanceImport::generateColumn($sheet, $column, $row?->get($column));
        });
    }

    private function generateDefectRecords($row)
    {
        return collect(EProjectPerformanceImport::DEFECT_COLUMNS)->keys()->map(function ($column) use ($row) {
            return EProjectPerformanceImport::generateDefect($column, $row?->get($column) ?? 0);
        });

    }

    /**
     * Process performance data for a single sprint
     *
     * @param object $sprint
     * @param \Illuminate\Support\Collection $overtimes
     * @return \Illuminate\Support\Collection
     */
    private function processSprintPerformance($sprint, $overtimes)
    {
        // Get basic sprint data
        $sprintData = $this->getSprintBasicData($sprint);

        // Calculate all effort values
        $effortValues = $this->calculateEffortValues($sprint, $overtimes);

        // Process functions
        $function = $this->processFunctions($sprint->functions, $effortValues->get('actual_logwork'));

        // Calculate effort metrics
        $effortMetrics = $this->calculateEffortMetrics($effortValues, $function);

        // Process defects
        $defect = $this->processDefect($sprint->defects ?? [], $effortValues->get('actual_logwork'));

        // Process quality gate
        $qualityGate = $this->processQualityGate($sprint->quality_gate);

        // Build and return the result
        return $sprintData
            ->merge($effortMetrics)
            ->merge($function)
            ->merge($qualityGate)
            ->merge(['defect' => $defect]);
    }

    /**
     * Get basic sprint data
     *
     * @param object $sprint
     * @return \Illuminate\Support\Collection
     */
    private function getSprintBasicData($sprint)
    {
        return collect([
            'id' => $sprint->id,
            'name' => $sprint->name,
            'start_date' => $sprint->start_date,
            'end_date' => $sprint->end_date,
        ]);
    }

    /**
     * Calculate all effort values for a sprint
     *
     * @param object $sprint
     * @param \Illuminate\Support\Collection $overtimes
     * @return \Illuminate\Support\Collection
     */
    private function calculateEffortValues($sprint, $overtimes)
    {
        $dailyReports = collect($sprint->daily_reports);
        $workingHoursInMonth = $this->getWorkingHoursInMonth();

        // Calculate actual logwork
        $actualLogWorks = $this->calculateActualLogworks($dailyReports, $workingHoursInMonth);

        // Calculate allocation effort
        $allocateEffort = $this->countAllocations($sprint->allocations);

        // Calculate overtime
        $ot = $this->calculateOvertime($sprint, $overtimes, $workingHoursInMonth);

        // Calculate absent (default to 0 for now)
        $absent = 0;

        // Calculate actual effort
        $actualEffort = $allocateEffort - $absent + $ot;

        return collect([
            'actual_logwork' => $actualLogWorks,
            'ot' => $ot,
            'absent' => $absent,
            'allocate_effort' => $allocateEffort,
            'actual_effort' => $actualEffort,
        ]);
    }

    /**
     * Calculate effort metrics for a sprint
     *
     * @param \Illuminate\Support\Collection $effortValues
     * @param array $function
     * @return \Illuminate\Support\Collection
     */
    private function calculateEffortMetrics($effortValues, $function) {
        // Calculate effort metrics
        $effortDeviation = $this->safeDivide(
            $effortValues->get('actual_logwork') + $effortValues->get('ot'),
            $effortValues->get('allocate_effort')
        );

        $effortEfficiency = $this->safeDivide(
            $function['estimate'],
            $effortValues->get('actual_effort')
        );

        return $effortValues->merge([
            'effort_deviation' => $effortDeviation,
            'effort_efficiency' => $effortEfficiency,
        ]);
    }

    /**
     * Calculate actual logworks from daily reports
     *
     * @param \Illuminate\Support\Collection $dailyReports
     * @param float $workingHoursInMonth
     * @return float
     */
    private function calculateActualLogworks($dailyReports, $workingHoursInMonth)
    {
        return $dailyReports->pluck('actual_time')->sum() / $workingHoursInMonth;
    }

    /**
     * Calculate overtime for a sprint
     *
     * @param object $sprint
     * @param \Illuminate\Support\Collection $overtimes
     * @param float $workingHoursInMonth
     * @return float
     */
    private function calculateOvertime($sprint, $overtimes, $workingHoursInMonth)
    {
        $overtimesInSprint = $this->getOvertimesInSprint($sprint, $overtimes);
        $totalOvertimesHoursWithCoefficient = $overtimesInSprint->sum('ot_with_coefficient');
        return $totalOvertimesHoursWithCoefficient / $workingHoursInMonth;
    }

    /**
     * Get overtimes that occurred during a sprint
     *
     * @param object $sprint
     * @param \Illuminate\Support\Collection $overtimes
     * @return \Illuminate\Support\Collection
     */
    private function getOvertimesInSprint($sprint, $overtimes)
    {
        return $overtimes
            ->where('date', '>=', $sprint->start_date)
            ->where('date', '<=', $sprint->end_date);
    }

    /**
     * Count total allocations with coefficients
     *
     * @param array $allocations
     * @return float
     */
    private function countAllocations($allocations)
    {
        return collect($allocations)
            ->map(fn ($allocation) => ($allocation->allocation * $allocation->coefficient) / 100)
            ->sum();
    }

    /**
     * Process functions data for a sprint
     *
     * @param array $functions
     * @param float $actualLogWorks
     * @return array
     */
    private function processFunctions($functions, $actualLogWorks)
    {
        $functions = collect($functions);
        $totalStoryPoint = $this->sumByProperty($functions, 'story_point');
        $totalEstimate = $this->sumByProperty($functions, 'estimate');
        $totalEarnedValued = $this->calculateTotalEarnedValue($functions);

        $productivity = $this->calculateProductivity($totalStoryPoint, $actualLogWorks);

        return [
            'story_point' => $totalStoryPoint,
            'productivity' => $productivity,
            'estimate' => $totalEstimate,
            'earned_valued' => $totalEarnedValued,
        ];
    }

    /**
     * Calculate productivity based on story points and actual logworks
     *
     * @param float $totalStoryPoint
     * @param float $actualLogWorks
     * @return float
     */
    private function calculateProductivity($totalStoryPoint, $actualLogWorks)
    {
        return $this->safeDivide(
            $totalStoryPoint,
            $actualLogWorks * EDailyReport::WORKING_DAYS_IN_MONTH
        );
    }

    /**
     * Calculate total earned value from functions
     *
     * @param \Illuminate\Support\Collection $functions
     * @return float
     */
    private function calculateTotalEarnedValue($functions)
    {
        return $functions
            ->map(fn ($function) => $this->calculateEarnedValued($function))
            ->sum();
    }

    /**
     * Sum values of a specific property in a collection
     *
     * @param \Illuminate\Support\Collection $collection
     * @param string $property
     * @return float
     */
    private function sumByProperty($collection, $property)
    {
        return $collection->pluck($property)->sum();
    }

    /**
     * Process quality gate data
     *
     * @param object|null $qualityGate
     * @return array
     */
    private function processQualityGate($qualityGate)
    {
        $weightedNc = $qualityGate?->number_of_non_compliance ?? 0;
        $processApply = $qualityGate?->number_of_process ?? 0;
        $pcv = $this->safeDivide($weightedNc, $processApply);

        return [
            'weighted_nc' => $weightedNc,
            'process_apply' => $processApply,
            'pcv' => $pcv,
            'number_of_incident' => $qualityGate?->number_of_incident ?? 0,
            'number_of_customer_complaint' => $qualityGate?->number_of_customer_complaint ?? 0,
            'quality_gate_type' => $qualityGate?->quality_gate ?? 0,
        ];
    }

    /**
     * Process defect data
     *
     * @param array $defects
     * @param float $actualLogWorks
     * @return array
     */
    private function processDefect($defects, $actualLogWorks)
    {
        $groupedDefect = $this->groupDefectsByTypeAndSeverity($defects);

        $defectStatistic = [];
        $totalDefect = 0;
        $removalEfficiencyDivisor = 0;

        foreach (EDefect::TYPES as $defectType) {
            $defectStatisticByType = $this->processDefectByType(
                $defectType,
                $groupedDefect,
                $actualLogWorks
            );

            $totalDefect += $defectStatisticByType['weighted'];
            if ($defectType === EDefect::REMOVAL_EFFICIENCY_TYPE) {
                $removalEfficiencyDivisor = $defectStatisticByType['weighted'];
            }
            $defectStatistic[] = $defectStatisticByType;
        }

        $defectDensity = $this->safeDivide($totalDefect, $actualLogWorks);
        $removalEfficiency = $this->safeDivide($removalEfficiencyDivisor, $totalDefect);

        return [
            'density' => $defectDensity,
            'weighted' => $totalDefect,
            'removal_efficiency' => $removalEfficiency,
            'statistic' => $defectStatistic,
        ];
    }

    /**
     * Group defects by type and severity
     *
     * @param array $defects
     * @return array
     */
    private function groupDefectsByTypeAndSeverity($defects)
    {
        return collect($defects)->groupBy('type')->map(function ($item) {
            return $item->groupBy('severity')->map(function ($item) {
                return $item->sum('total');
            });
        })->toArray();
    }

    /**
     * Process defects for a specific type
     *
     * @param string $defectType
     * @param array $groupedDefect
     * @param float $actualLogWorks
     * @return array
     */
    private function processDefectByType($defectType, $groupedDefect, $actualLogWorks)
    {
        $totalDefectByType = 0;
        $defectStatisticByType = [];
        $defectByType = @$groupedDefect[$defectType] ?? [];
        $defectStatisticByType['type'] = $defectType;
        $defectStatisticByType['count'] = [];

        foreach (EDefect::SEVERITIES as $severity) {
            $totalDefectBySeverity = @$defectByType[$severity] ?? 0;
            $defectStatisticByType['count'][] = [
                'severity' => $severity,
                'total' => $totalDefectBySeverity,
            ];
            $totalDefectByType += $totalDefectBySeverity * EDefect::COEFFICIENTS[$severity];
        }

        $defectStatisticByType['density'] = $this->safeDivide($totalDefectByType, $actualLogWorks);
        $defectStatisticByType['weighted'] = $totalDefectByType;

        return $defectStatisticByType;
    }

    /**
     * Calculate earned value for a function
     *
     * @param object $function
     * @return float
     */
    private function calculateEarnedValued($function)
    {
        return $function->estimate * EFunction::EARNED_VALUE_COEFFICIENTS[$function->work_completed];
    }

    private function sumColumn($array, $data, $column)
    {
        $sum = array_sum(array_column($data, $column));
        if ($sum) {
            $array[$column] = $sum;
        }

        return $array;
    }

    private function performance($data, $column, $columnDivisor, $columnDividend)
    {
        if (@$data[$columnDivisor] && @$data[$columnDividend]) {
            $data[$column] = $data[$columnDivisor] / $data[$columnDividend];
        }

        return $data;
    }
}
