<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;

class CustomerService
{
    use ProjectServiceSupport;

    public function __construct()
    {
    }

    public function getCustomers($args)
    {
        return $this->__getCustomers($args);
    }

    public function getListCustomer($args)
    {
        $customers = $this->__getListCustomer($args);
        collect($customers->data)->map(function ($customer) {
            $projects = collect($customer->projects)->map(function ($project) {
                return collect($project)
                    ->only(['id', 'name'])
                    ->all();
            });

            $customer->projects = $projects;
        });

        return $customers;
    }

    public function store($args)
    {
        $customer = $this->__createCustomer($args);

        return $customer;
    }

    public function syncCustomers()
    {
        return $this->__syncCustomers();
    }

    public function update($args, $id)
    {
        return $this->__updateCustomer($args, $id);
    }

    public function destroy($id)
    {
        return $this->__deleteCustomer($id);
    }
}
