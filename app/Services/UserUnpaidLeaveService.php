<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;
use App\Traits\UserServiceSupport;

class UserUnpaidLeaveService
{
    use ProjectServiceSupport;
    use UserServiceSupport;

    public function getListUserUnpaidLeave($args)
    {
        $usersUnpaidLeave = $this->__getListUserUnpaidLeave($args);

        $userIds = array_column($usersUnpaidLeave->data, 'user_id');

        $users = $this->__getUsers([
            'user_ids' => $userIds
        ]);

        $users = collect($users)->keyBy('user_id')->all();

        $usersUnpaidLeave->data = collect($usersUnpaidLeave->data)->map(function ($item) use ($users) {
            $userId = $item->user_id;
            $item->name = $users[$userId]->name ?? null;
            $item->email = $users[$userId]->email ?? null;
            $item->code = $users[$userId]->code ?? null;

            return $item;
        });

        return $usersUnpaidLeave;
    }

    public function store($args)
    {
        return $this->__createUserUnpaidLeave($args);
    }

    public function update($args, $id)
    {
        return $this->__updateUserUnpaidLeave($args, $id);
    }

    public function destroy($id)
    {
        return $this->__deleteUserUnpaidLeave($id);
    }
}
