<?php

namespace App\Services;

use App\Enums\EBase;
use App\Enums\EContractType;
use App\Enums\EDailyReport;
use App\Repositories\UserRepository;
use App\Traits\ExtraFunctions;
use App\Traits\UserServiceSupport;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use DatePeriod;
use DateTime;
use stdClass;

class ResourceService
{
    use UserServiceSupport, ExtraFunctions;

    private $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getResources($args)
    {
        if (!isset($args['fields'])) {
            $args['fields'] = ['user_id', 'name', 'code', 'division', 'position', 'skills', 'level', 'email', 'onboard_date', 'checkout_date', 'status', 'team', 'completed_projects'];
        }
        $args['pagination'] = (isset($args['pagination'])) ? $args['pagination'] : false;

        $users = $this->userRepository->getUsers($args);
        
        return json_decode($users->toJson());
    }

    public function mapResourceDailyReport($resources, $dailyReports)
    {
        $resources->map(function ($resource) use ($dailyReports) {
            $reports = array_filter($dailyReports, fn ($item) => $item->user_id == $resource->user_id);

            $resource->daily_reports = array_values($reports);
        });

        return $resources;
    }

    public function mapResourceEffort($resources, $holiday, $v2ApplyDate, $startDate, $endDate)
    {
        $resources->transform(function ($resource) use ($holiday, $v2ApplyDate, $startDate, $endDate) {
            $months = collect([]);
            $weeks = collect([]);
            $dateAllocate = [];
            /**
             * xảy ra 2 trường hợp 1 là có allocate và daily report, 2 là chỉ có daily report
             * vì vậy cần phải tính effort cho cả 2 trường hợp
             */
            foreach ($resource->allocates as $allocate) {
                $monthsBetweenAllocate = $this->getMonthsBetween($allocate->start_date, $allocate->end_date);
                $weeksBetweenAllocate = $this->weeksBetweenTwoDates($allocate->start_date, $allocate->end_date);
                $daysBetween = CarbonPeriod::create($allocate->start_date, $allocate->end_date)->filter(fn (Carbon $date) => $date->isWeekday());
                $months->push($monthsBetweenAllocate);
                $weeks->push($weeksBetweenAllocate);

                foreach ($daysBetween as $date) {

                    if (isset($dateAllocate[$date->format('Y-m-d')])) {
                        $dateAllocate[$date->format('Y-m-d')] += $allocate->allocation;
                    } else {
                        $dateAllocate[$date->format('Y-m-d')] = $allocate->allocation;
                    }
                }
            }
            foreach ($resource->daily_reports as $report) {
                $workTime = Carbon::parse($report->work_date);
                $weeksBetweenAllocate = $this->weeksBetweenTwoDates($workTime->startOfWeek()->format('Y-m-d'), $workTime->endOfWeek()->format('Y-m-d'));
                $months->push($workTime->format('Y-m-d'));
                $weeks->push($weeksBetweenAllocate);
            }

            $months = $months->flatten()->map(fn ($month) => substr($month, 0, -3))->unique();
            $weeks = $weeks->flatten(1)->unique('title');
            $months->each(function ($month) use ($resource, $holiday, $v2ApplyDate, $startDate, $endDate) {
                $tmpMonth = Carbon::parse($month);
                $from = $tmpMonth->startOfMonth()->format('Y-m-d');
                $to = $tmpMonth->endOfMonth()->format('Y-m-d');
                $logWorkHours = 0;
                $calendarEffort = 0;
                $allocateEffort = 0;
                $logWorkEffort = 0;
                $effortBeforeV2ApplyDate = 0;
                $leaveHours = 0;
                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->gte($to) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $effortBeforeV2ApplyDate = $this->calCalendarEffortBeforeV2ApplyDate(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave, $v2ApplyDate);
                }
                $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday, $resource->unpaid_leave ?? [], $resource->onboard_date ?? null, $resource->checkout_date ?? null);
                if (isset($resource->reports_of_month[$month]) && Carbon::today()->gt($from) && Carbon::parse($v2ApplyDate)->lt($to)) {
                    $dailyReports = $resource->reports_of_month[$month];
                    $logWorkHours = $this->calLogWorkEffort($dailyReports);
                }
                if (isset($resource->days_leave) && $resource->days_leave->isNotEmpty()) {
                    $resource->days_leave->each(function ($dayLeave) use ($from, $to, &$leaveHours) {
                        if (Carbon::parse($dayLeave->created_at)->between($from, $to)) {
                            $shifts = explode(',', $dayLeave->paid_leave);
                            foreach ($shifts as $shift) {
                                if ($shift != 0) $leaveHours += 2;
                            };
                        }
                    });
                }
                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->lt($from) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $calendarEffort = $this->calCalendarEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);
                }
                if (isset($resource->allocates)) $allocateEffort = $this->calAllocateEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);

                $totalWorkingHours = $actualWorkingDays * 8 - $leaveHours ?? 0;
                if ($actualWorkingDays == 0) $actualWorkingDays = 1;
                if ($totalWorkingHours == 0) $totalWorkingHours = 1;
                $totalEffort = $calendarEffort + $logWorkHours + $effortBeforeV2ApplyDate - $leaveHours ?? 0;
                $actualEffort = $totalEffort / $totalWorkingHours * 100;
                $logWorkEffort = $logWorkHours / $totalWorkingHours * 100;
                $resource->month_schedule[] = [
                    'title' => date('Y', strtotime($month)) . ' - ' . 'Tháng ' . date('m', strtotime($month)),
                    'actual_effort' => $actualEffort,
                    'allocate_effort' => ($allocateEffort / ($actualWorkingDays * 8)) * 100,
                    'total_working_hour' => $actualWorkingDays * 8,
                    'total_daily_report' => $logWorkHours,
                    'daily_report_effort' => $logWorkEffort,
                    'start_date' => $from,
                    'end_date' => $to,
                ];
            });

            $weeks->each(function ($week) use ($resource, $holiday, $v2ApplyDate, $startDate, $endDate) {
                $tmpWeek = Carbon::parse($week['start_date']);
                $from = $tmpWeek->startOfWeek()->format('Y-m-d');
                $to = $tmpWeek->endOfWeek()->format('Y-m-d');
                $logWorkHours = 0;
                $calendarEffort = 0;
                $allocateEffort = 0;
                $logWorkEffort = 0;
                $effortBeforeV2ApplyDate = 0;
                $leaveHours = 0;

                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->gte($to) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $effortBeforeV2ApplyDate = $this->calCalendarEffortBeforeV2ApplyDate(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave, $v2ApplyDate);
                }
                $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday, $resource->unpaid_leave ?? [], $resource->onboard_date ?? null, $resource->checkout_date ?? null);

                if (isset($resource->reports_of_week[$week['week']]) && Carbon::today()->gt($from) && Carbon::parse($v2ApplyDate)->lt($to)) {
                    $dailyReports = $resource->reports_of_week[$week['week']];
                    $logWorkHours = $this->calLogWorkEffort($dailyReports);
                }
                if (!empty($resource->days_leave)) {
                    collect($resource->days_leave)->each(function ($dayLeave) use ($from, $to, &$leaveHours) {
                        if (Carbon::parse($dayLeave->created_at)->between($from, $to)) {
                            $shifts = explode(',', $dayLeave->paid_leave);
                            foreach ($shifts as $shift) {
                                if ($shift != 0) $leaveHours += 2;
                            };
                        }
                    });
                }

                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->lt($from) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $calendarEffort = $this->calCalendarEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);
                }
                if (isset($resource->allocates)) $allocateEffort = $this->calAllocateEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);

                $totalWorkingHours = $actualWorkingDays * 8 - $leaveHours;
                if ($totalWorkingHours == 0) $totalWorkingHours = 1;
                $actualEffort = ($calendarEffort + $logWorkHours + $effortBeforeV2ApplyDate) / $totalWorkingHours * 100;
                $logWorkEffort = $logWorkHours / $totalWorkingHours * 100;
                $resource->week_schedule[] = [
                    'title' => $week['title'],
                    'actual_effort' => $actualEffort,
                    'allocate_effort' => $allocateEffort / $totalWorkingHours * 100,
                    'total_working_hour' => $totalWorkingHours,
                    'total_daily_report' => $logWorkHours,
                    'daily_report_effort' => $logWorkEffort,
                    'start_date' => $from,
                    'end_date' => $to,
                ];
            });
            // day
            $allocates = empty($dateAllocate) ? $this->getAllDays($startDate, $endDate) : $dateAllocate;
            $dailyReports = $resource->daily_reports;
            $resource->day_schedule = array_map(function ($date) use ($allocates, $dailyReports, $v2ApplyDate) {
                $allocateEffort = $allocates[$date] ?? 0;
                $dailyReports = collect($dailyReports)
                    ->filter(function ($report) use ($date) {
                        return Carbon::parse($report->work_date)->equalTo($date);
                    })->values();
                $totalDailyReport = $dailyReports->sum('actual_time');
                $dailyReportEffort = ($totalDailyReport / EBase::WORKING_DAY_HOURS) * 100;
                $dailyReportEffort = round($dailyReportEffort, 2);
                $actualEffort = Carbon::parse($date)->lessThan(today()) ? $dailyReportEffort : $allocateEffort;

                if (Carbon::parse($date)->lessThan($v2ApplyDate)) {
                    $dailyReportEffort = $allocateEffort;
                    $actualEffort = $allocateEffort;
                }

                return [
                    'work_date' => $date,
                    'total_daily_report' => $totalDailyReport,
                    'daily_report_effort' => $dailyReportEffort,
                    'allocate_effort' => $allocateEffort,
                    'actual_effort' => $actualEffort
                ];
            }, $this->generateDateRanges($startDate, $endDate));

            unset($resource->daily_reports, $resource->reports_of_month, $resource->reports_of_week, $resource->days_leave);
            return $resource;
        });

        return $resources;
    }

    public function mapResourceDayEffort($resources, $holiday, $v2ApplyDate, $from, $to)
    {
        $resources->transform(function ($resource) use ($holiday, $v2ApplyDate, $from, $to) {
            $dateAllocate = [];

            foreach ($resource->allocates as $allocate) {
                $daysBetween = CarbonPeriod::create($allocate->start_date, $allocate->end_date)->filter(fn (Carbon $date) => $date->isWeekday());

                foreach ($daysBetween as $date) {
                    if (isset($dateAllocate[$date->format('Y-m-d')])) {
                        $dateAllocate[$date->format('Y-m-d')] += $allocate->allocation;
                    } else {
                        $dateAllocate[$date->format('Y-m-d')] = $allocate->allocation;
                    }
                }
            }

            $allocates = empty($dateAllocate) ? $this->getAllDays($from, $to) : $dateAllocate;
            $dailyReports = $resource->daily_reports;
            $resource->day_schedule = array_map(function ($date) use ($allocates, $dailyReports, $v2ApplyDate) {
                $allocateEffort = $allocates[$date] ?? 0;
                $dailyReports = collect($dailyReports)
                    ->filter(function ($report) use ($date) {
                        return Carbon::parse($report->work_date)->equalTo($date);
                    })->values();
                $totalDailyReport = $dailyReports->sum('actual_time');
                $dailyReportEffort = ($totalDailyReport / EBase::WORKING_DAY_HOURS) * 100;
                $dailyReportEffort = round($dailyReportEffort, 2);
                $actualEffort = Carbon::parse($date)->lessThan(today()) ? $dailyReportEffort : $allocateEffort;

                if (Carbon::parse($date)->lessThan($v2ApplyDate)) {
                    $dailyReportEffort = $allocateEffort;
                    $actualEffort = $allocateEffort;
                }

                return [
                    'work_date' => $date,
                    'total_daily_report' => $totalDailyReport,
                    'daily_report_effort' => $dailyReportEffort,
                    'allocate_effort' => $allocateEffort,
                    'actual_effort' => $actualEffort
                ];
            }, $this->generateDateRanges($from, $to));

            unset($resource->skills, $resource->level, $resource->position);
            return $resource;
        });

        return $resources;
    }

    public function calDivBusyRateInYear($resources, $months, $holiday, $v2ApplyDate, $onlyAllocate)
    {
        $allUser = [];
        $resources = $resources->map(function ($resource) use ($holiday, &$allUser, $months, $v2ApplyDate, $onlyAllocate) {
            $months->each(function ($month) use ($resource, $holiday, $v2ApplyDate, $onlyAllocate) {
                $tmpMonth = Carbon::parse($month);
                $from = $tmpMonth->startOfMonth()->format('Y-m-d');
                $to = $tmpMonth->endOfMonth()->format('Y-m-d');
                $logWorkEffort = 0;
                $calendarEffort = 0;
                $effortBeforeV2ApplyDate = 0;
                $leaveHours = 0;

                $checkUserInvalid = true;
                if (isset($resource->onboard_date) && Carbon::createFromDate($to)->lt($resource->onboard_date)) {
                    $checkUserInvalid = false;
                }
                if (isset($resource->checkout_date) && $resource->status == 0 && Carbon::createFromDate($resource->checkout_date)->lt($from)) {
                    $checkUserInvalid = false;
                }
                if (!isset($resource->checkout_date) && $resource->status == 0) {
                    $checkUserInvalid = false;
                }
                if (!empty($resource->unpaid_leave)) {
                    $checkUserInvalid = !collect($resource->unpaid_leave)->some(function ($unpaidLeave) use ($from, $to) {
                        $startLeave = Carbon::createFromDate($unpaidLeave->start_date);
                        $endLeave = Carbon::createFromDate($unpaidLeave->end_date);
                        return ($startLeave->lte($from) && $endLeave->gte($to));
                    });
                }

                if ($checkUserInvalid == false) {
                    $resource->effort[$month] = ['effort' => 0, 'working_hours' => 0];
                    return $resource;
                };

                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->gte($to) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $effortBeforeV2ApplyDate = $this->calCalendarEffortBeforeV2ApplyDate(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave, $v2ApplyDate);
                }
                $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday, $resource->unpaid_leave ?? [], $resource->onboard_date ?? null, $resource->checkout_date ?? null);

                if (
                    isset($resource->daily_reports[$month])
                    && Carbon::today()->gt($from)
                    && Carbon::parse($v2ApplyDate)->lt($to)
                ) {
                    $dailyReports = $resource->daily_reports[$month];
                    $logWorkEffort = $this->calLogWorkEffort($dailyReports);
                }

                if (!empty($resource->days_leave)) {
                    collect($resource->days_leave)->each(function ($dayLeave) use ($from, $to, &$leaveHours) {
                        if (Carbon::parse($dayLeave->created_at)->between($from, $to)) {
                            $shifts = explode(',', $dayLeave->paid_leave);
                            foreach ($shifts as $shift) {
                                if ($shift != 0) $leaveHours += 2;
                            };
                        }
                    });
                }

                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->lt($from) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $calendarEffort = $this->calCalendarEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);
                }
                if ($onlyAllocate == true) $leaveHours = 0;
                $effort = $calendarEffort + $logWorkEffort + $effortBeforeV2ApplyDate - $leaveHours ?? 0;
                if ((int)$effort < 0) $effort = 0;
                $resource->effort[$month] = [
                    'effort' => $effort,
                    'working_hours' => $actualWorkingDays * 8 - $leaveHours ?? 0
                ];
            });
            $allUser[] = $resource;
            return $resource;
        })->groupBy(function ($resource) {
            if (isset($resource->division)) {
                return $resource->division->division_name ?? $resource->division->name;
            }
        });
        $resources->put('Amela', collect($allUser));
        $divisionEffort = [];
        $months->each(function ($month) use ($resources, &$divisionEffort) {
            $args['from'] = Carbon::createFromDate($month)->firstOfMonth()->format('Y-m-d');
            $args['to'] = Carbon::createFromDate($month)->endOfMonth()->format('Y-m-d');
            $resources->each(function ($resourceDivision, $div) use ($month, &$divisionEffort) {
                $resourceDivision->each(function ($resource) use ($month, &$divisionEffort, $div) {
                    if (!isset($divisionEffort[$div][$month]['effort'])) {
                        $divisionEffort[$div][$month]['effort'] = 0;
                        $divisionEffort[$div][$month]['working_hours'] = 0;
                    }
                    $divisionEffort[$div][$month]['effort'] += $resource->effort[$month]['effort'];
                    $divisionEffort[$div][$month]['working_hours'] += $resource->effort[$month]['working_hours'];
                });
            });
        });

        $busyRateInMonth = [];
        foreach ($divisionEffort as $div => $monthEffort) {
            foreach ($monthEffort as $month => $item) {
                $divMonthBusyRate = ['division' => $div, 'month' => $month];
                $divMonthBusyRate['value'] =  $item['working_hours'] != 0 ? ($item['effort'] * 100 / $item['working_hours']) : 0;
                $busyRateInMonth[] = $divMonthBusyRate;
            }
        }
        return $busyRateInMonth;
    }

    public function monthAllocate($allocate, $holiday, $unpaidLeave = null)
    {
        $months = $this->getMonthsBetween($allocate->start_date, $allocate->end_date);
        $monthAllocate = [];
        foreach ($months as $month) {
            if ($unpaidLeave->isNotEmpty()) {
                $startOfMonth = Carbon::createFromDate($month)->startOfMonth();
                $endOfMonth = Carbon::createFromDate($month)->endOfMonth();
                $unpaidLeave = $unpaidLeave
                    ->filter(fn ($leave) => $startOfMonth->between($leave->start_date, $leave->end_date) || $endOfMonth->between($leave->start_date, $leave->end_date))
                    ->map(function ($unpaidLeave) use ($startOfMonth, $endOfMonth) {
                        $startLeave = Carbon::createFromDate($unpaidLeave->start_date);
                        $endLeave = Carbon::createFromDate($unpaidLeave->end_date);
                        if ($startLeave->lte($startOfMonth) && $endLeave->gt($startOfMonth) && $endLeave->lte($endOfMonth)) {
                            $unpaidLeave->start_date = $startOfMonth->format('Y-m-d');
                        }
                        if ($endLeave->gte($endOfMonth) && $startLeave->gte($startOfMonth) && $startLeave->lte($endOfMonth)) {
                            $unpaidLeave->end_date = $endOfMonth->format('Y-m-d');
                        }
                        return $unpaidLeave;
                    });
                $allocate->days_leave = $unpaidLeave->reduce(function ($prev, $leave) use ($holiday) {
                    $daysLeave = $this->numberOfWorkingDays($leave->start_date, $leave->end_date, $holiday);
                    return $prev + $daysLeave;
                }, 0);
            }
            $monthYmFormat = Carbon::parse($month)->format('Y-m');
            $startDate = Carbon::parse($allocate->start_date)->format('Y-m');
            $endDate = Carbon::parse($allocate->end_date)->format('Y-m');
            if ($monthYmFormat >= $startDate || $monthYmFormat <= $endDate) {
                $allocatedOfMonth = $this->calManMonthSingleMonthWithOnboardDate(
                    $allocate->start_date,
                    $allocate->end_date,
                    $allocate->allocation,
                    $month,
                    $allocate->onboard_date ?? null,
                    $holiday,
                    $allocate->days_leave ?? null
                );
                if (isset($monthAllocate[$monthYmFormat])) {
                    $monthAllocate[$monthYmFormat] += $allocatedOfMonth;
                } else {
                    $monthAllocate[$monthYmFormat] = $allocatedOfMonth;
                }
            }
        }

        return $monthAllocate;
    }


    public function calActualWorkingDay($allocate, $week, $holiday)
    {
        if ($allocate['start_date'] > $week['start_date']) {
            $from = $allocate['start_date'];
            $to = $week['end_date'];
        } elseif ($allocate['end_date'] < $week['end_date']) {
            $from = $week['start_date'];
            $to = $allocate['end_date'];
        } else {
            $from = $week['start_date'];
            $to = $week['end_date'];
        }
        $from = Carbon::createFromFormat('Y-m-d', $from);
        $to = Carbon::createFromFormat('Y-m-d', $to);

        return (int) $this->numberOfWorkingDays($from->format('Y-m-d'), $to->format('Y-m-d'), $holiday);
    }

    public function weeksBetweenTwoDates($startDate = null, $endDate = null)
    {
        $date1 = new DateTime($startDate);
        $date2 = new DateTime($endDate);

        $interval = $date1->diff($date2);
        $weeks = ceil(($interval->days + 1) / 7);
        if (($date1->format('N') > 1)) {
            $different = '-' . ($date1->format('N') - 1) . ' Days';
            $date1 = $date1->modify($different);
        }
        $result = [];
        for ($i = 0; $i <= $weeks; $i++) {
            if ($i == 0) {
                $start_date = $date1->format('d/m');
                $startDate = $date1->format('Y-m-d');
                $date1->add(new DateInterval('P6D'));
            } else {
                $date1->add(new DateInterval('P6D'));
            }
            $result[$i]['title'] = date('Y', strtotime($startDate)) . ' - ' . 'Tuần ' . $this->weekOfYear(strtotime($startDate))
                . ' (' . $start_date . ' - ' . $date1->format('d/m') . ')';
            $result[$i]['start_date'] = $startDate;
            $result[$i]['end_date'] = $date1->format('Y-m-d');
            $result[$i]['week'] = $this->weekOfYear(strtotime($startDate));
            $date1->add(new DateInterval('P1D'));
            $start_date = $date1->format('d/m');
            $startDate = $date1->format('Y-m-d');
        }

        return $result;
    }

    public function weekOfMonth($date)
    {
        //Get the first day of the month.
        $firstOfMonth = strtotime(date('Y-m-01', $date));
        //Apply above formula.
        return $this->weekOfYear($date) - $this->weekOfYear($firstOfMonth) + 1;
    }

    public function weekOfYear($date)
    {
        $weekOfYear = intval(date('W', $date));
        if (date('n', $date) == '1' && $weekOfYear > 51) {
            // It's the last week of the previos year.
            return 0;
        } elseif (date('n', $date) == '12' && $weekOfYear == 1) {
            // It's the first week of the next year.
            return 53;
        } else {
            // It's a "normal" week.
            return $weekOfYear;
        }
    }

    public function getAllMonths($startDate, $endDate)
    {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $months = array();

        // Loop from start date to end date, incrementing by 1 month each time
        while ($start <= $end) {
            $months[$start->format('Y-m')] = 0;
            $start->modify('+1 month');
        }

        return $months;
    }

    public function getAllDays($startDate, $endDate)
    {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $days = array();

        // Loop from start date to end date, incrementing by 1 day each time
        while ($start <= $end) {
            $days[$start->format('Y-m-d')] = 0;
            $start->modify('+1 day');
        }

        return $days;
    }

    public function getMonthsBetweenFormatYm($start_date, $end_date)
    {
        $start = new DateTime($start_date);
        $end = new DateTime($end_date);
        $start->modify('first day of this month');
        $end->modify('first day of next month');
        $interval = DateInterval::createFromDateString('1 month');
        $period = new DatePeriod($start, $interval, $end);
        $months = [];
        foreach ($period as $month) {
            $months[] = $month->format('Y-m');
        }

        return $months;
    }

    public function mapResourceAllocationByChartType($chartType, $resources, $allocations, $startDate, $endDate, $holiday, $userUnpaidLeave)
    {
        $workTimeManMonthByUserId = $resources->keyBy('user_id')->map(function ($resource, $userId) use ($startDate, $endDate, $allocations, $holiday, $userUnpaidLeave) {
            $allocates = array_filter(
                $allocations,
                fn ($allocation) => $allocation->user_id == $userId
            );
            $unpaidLeaveOfEachUser = $userUnpaidLeave->filter(fn ($item) => $item->user_id === $userId);
            $numberOfWorkingHours = $this->getNumberOfWorkingHours(
                $startDate,
                $endDate,
                $holiday,
                $unpaidLeaveOfEachUser,
                $resource->onboard_date ?? null,
                $resource->checkout_date ?? null
            );
            if ($numberOfWorkingHours === 0) {
                return 0;
            }
            $allocateNumber = array_reduce(
                $allocates,
                function ($prev, $item) use ($startDate, $endDate, $holiday, $unpaidLeaveOfEachUser, $resource, $numberOfWorkingHours) {
                    $startWorkDate = $resource->onboard_date ?? $item->start_date;
                    $endWorkDate = $resource->checkout_date ?? $item->end_date;
                    $numberOfAllocatedHours = $this->getNumberOfWorkingHours(
                        max($startDate, $item->start_date, $startWorkDate),
                        min($endDate, $item->end_date, $endWorkDate),
                        $holiday,
                        $unpaidLeaveOfEachUser,
                        $resource->onboard_date ?? null,
                        $resource->checkout_date ?? null
                    );
                    return $prev + (($item->allocation * $numberOfAllocatedHours) / (100 * $numberOfWorkingHours));
                },
                0
            );
            return $allocateNumber;
        });
        $groupedResources = $this->groupResourceByChartType($resources, $chartType);
        return $this->mapResourceToFreeAllocatedOverload($groupedResources, $workTimeManMonthByUserId);
    }

    public function mapResourceDailyReportByChartType($chartType, $resources, $dailyReports, $startDate, $endDate, $holiday, $userUnpaidLeave)
    {
        $reportsByUserId = collect($dailyReports)->groupBy(fn ($report) => $report->user_id);
        $hoursPaidLeaveByUserId = $this->getNumberHoursLeave(
            $resources->pluck('user_id')->toArray() ?? null,
            $startDate,
            min(Carbon::today()->format('Y-m-d'), $endDate)
        );

        $workTimeManMonthByUserId = $reportsByUserId->map(function ($reports, $userId) use ($resources, $startDate, $endDate, $holiday, $userUnpaidLeave, $hoursPaidLeaveByUserId) {
            $userResource = $resources->first(function ($resource) use ($userId) {
                return $resource->user_id === $userId;
            });
            $numberOfPaidLeaveHours = $hoursPaidLeaveByUserId[$userId] ?? 0;
            $onboardDate = $userResource->onboard_date ?? null;
            $checkoutDate = $userResource->checkout_date ?? null;
            $unpaidLeaveOfEachUser = $userUnpaidLeave->filter(fn ($item) => $item->user_id === $userId);
            $numberOfWorkingHours = $this->getNumberOfWorkingHours($startDate, $endDate, $holiday, $unpaidLeaveOfEachUser, $onboardDate, $checkoutDate) - $numberOfPaidLeaveHours;
            $reportLimitStartDate = $onboardDate ?? $startDate;
            $reportLimitEndDate = $checkoutDate ?? $endDate;
            $reportsInTimeRange = $reports->filter(function ($report) use ($reportLimitStartDate, $reportLimitEndDate) {
                return $reportLimitStartDate <= $report->work_date && $report->work_date <= $reportLimitEndDate;
            });
            return $numberOfWorkingHours > 0 ? (array_sum(
                    $reportsInTimeRange->map(fn ($report) => $report->actual_time)->toArray()
                ) / $numberOfWorkingHours) : 0;
        });

        $groupedResources = $this->groupResourceByChartType($resources, $chartType);
        return $this->mapResourceToFreeAllocatedOverload($groupedResources, $workTimeManMonthByUserId);
    }

    private function getNumberHoursLeave($userIds, $startDate, $endDate)
    {
        $paramsGetDaysLeave = [
            'user_ids' => $userIds ?? null,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'columns' => ['user_id', 'created_at', 'paid_leave']
        ];
        $daysLeave = $this->__getDayLeave($paramsGetDaysLeave);

        $daysWithHoursLeave = collect($daysLeave)->map(function ($day) {
            $shifts = explode(',', $day->paid_leave);
            $leaveHours = 0;
            foreach ($shifts as $shift) {
                if ($shift != 0) $leaveHours += 2;
            };
            $day->leave_hours = $leaveHours;
            return $day;
        });

        $hoursPaidLeaveByUserId = $daysWithHoursLeave->groupBy('user_id')->map(function ($leaveDays, $userId) {
            return array_sum(
                collect($leaveDays)->map(fn ($day) => $day->leave_hours)->toArray()
            );
        });
        return $hoursPaidLeaveByUserId;
    }

    private function mapResourceToFreeAllocatedOverload($groupedResources, $workTimeManMonthByUserId)
    {
        $reportedManMonth = $groupedResources
            ->map(function ($members, $key) use ($workTimeManMonthByUserId) {
                $freeTimeArr = $members->map(function ($member) use ($workTimeManMonthByUserId) {
                    $workTime = $workTimeManMonthByUserId[$member->user_id] ?? 0;
                    return $workTime < 1 ? 1 - $workTime : 0;
                })->toArray();
                $allocatedTimeArr = $members->map(function ($member) use ($workTimeManMonthByUserId) {
                    $workTime = $workTimeManMonthByUserId[$member->user_id] ?? 0;
                    return $workTime < 1 ? $workTime : 1;
                })->toArray();
                $overloadTimeArr = $members->map(function ($member) use ($workTimeManMonthByUserId) {
                    $workTime = $workTimeManMonthByUserId[$member->user_id] ?? 0;
                    return $workTime >= 1 ? $workTime - 1 : 0;
                })->toArray();

                return [
                    'free' => array_sum($freeTimeArr),
                    'allocated' => array_sum($allocatedTimeArr),
                    'overload' => array_sum($overloadTimeArr)
                ];
            });
        return $reportedManMonth;
    }

    public function mapResourceDailyReportAllocationByChartType($chartType, $resources, $dailyReports, $allocations, $startDate, $endDate, $holiday, $userUnpaidLeave)
    {
        $today = Carbon::now()->format('Y-m-d');
        $reportsByUserId = collect($dailyReports)->groupBy(fn ($report) => $report->user_id);
        $hoursPaidLeaveByUserId = $this->getNumberHoursLeave(
            $resources->pluck('user_id')->toArray() ?? null,
            $startDate,
            min(Carbon::today()->format('Y-m-d'), $endDate)
        );
        $reportedTimeManMonthByUserId = $reportsByUserId->map(function ($reports, $userId) use ($resources, $startDate, $endDate, $holiday, $userUnpaidLeave, $hoursPaidLeaveByUserId) {
            $userResource = $resources->first(function ($resource) use ($userId) {
                return $resource->user_id === $userId;
            });
            if (!$userResource) {
                return 0;
            }
            $numberOfPaidLeaveHours = $hoursPaidLeaveByUserId[$userId] ?? 0;
            $reportLimitStartDate = $userResource->onboard_date ?? $startDate;
            $reportLimitEndDate = $userResource->checkout_date ?? $endDate;
            $reportsInTimeRange = $reports->filter(function ($report) use ($reportLimitStartDate, $reportLimitEndDate) {
                return $reportLimitStartDate <= $report->work_date && $report->work_date <= $reportLimitEndDate;
            });
            $unpaidLeaveOfEachUser = $userUnpaidLeave->filter(fn ($item) => $item->user_id === $userId);
            $numberOfWorkingHours = $this->getNumberOfWorkingHours(
                $startDate,
                $endDate,
                $holiday,
                $unpaidLeaveOfEachUser,
                $userResource->onboard_date ?? null,
                $userResource->checkout_date ?? null
            ) - $numberOfPaidLeaveHours;

            return $numberOfWorkingHours > 0 ?
                (array_sum(
                        $reportsInTimeRange->map(fn ($report) => $report->actual_time)->toArray()
                    ) / $numberOfWorkingHours)
                : 0;
        });

        $allocatedResourceByUserId = $resources->keyBy('user_id')->map(
            function ($resource, $userId) use ($startDate, $endDate, $allocations, $holiday, $today, $userUnpaidLeave, $hoursPaidLeaveByUserId) {
                $allocates = array_filter(
                    $allocations,
                    fn ($allocation) => $allocation->user_id === $userId
                );
                $numberOfPaidLeaveHours = $hoursPaidLeaveByUserId[$userId] ?? 0;
                $unpaidLeaveOfEachUser = $userUnpaidLeave->filter(fn ($item) => $item->user_id === $userId);
                $numberOfWorkingHours = $this->getNumberOfWorkingHours(
                    $startDate,
                    $endDate,
                    $holiday,
                    $unpaidLeaveOfEachUser,
                    $resource->onboard_date ?? null,
                    $resource->checkout_date ?? null
                ) - $numberOfPaidLeaveHours;

                $result = $numberOfWorkingHours > 0 ? array_reduce(
                    $allocates,
                    function ($prev, $item) use ($endDate, $holiday, $unpaidLeaveOfEachUser, $resource, $today, $numberOfWorkingHours) {
                        $startWorkDate = $resource->onboard_date ?? $item->start_date;
                        $endWorkDate = $resource->checkout_date ?? $item->end_date;
                        $numberOfAllocatedHours = $this->getNumberOfWorkingHours(
                            max($today, $item->start_date, $startWorkDate),
                            min($endDate, $item->end_date, $endWorkDate),
                            $holiday,
                            $unpaidLeaveOfEachUser,
                            $resource->onboard_date ?? null,
                            $resource->checkout_date ?? null
                        );
                        return $prev + (($item->allocation * $numberOfAllocatedHours) / (100 * $numberOfWorkingHours));
                    },
                    0
                ) : 0;
                return $result;
            }
        );

        $workTimeByUserId = $allocatedResourceByUserId->map(function ($allocatedFuture, $userId) use ($reportedTimeManMonthByUserId) {
            return $allocatedFuture + ($reportedTimeManMonthByUserId[$userId] ?? 0);
        });
        $groupedResources = $this->groupResourceByChartType($resources, $chartType);
        return $this->mapResourceToFreeAllocatedOverload($groupedResources, $workTimeByUserId);
    }

    private function groupResourceByChartType($resources, $chartType)
    {
        switch ($chartType) {
            case 'position':
                $groupedResources =  $resources->groupBy(fn ($resource) => $resource->position->name);
                break;
            case 'division':
                $groupedResources =  $resources->groupBy(function ($resource) {
                    if (isset($resource->division)) {
                        return $resource->division->division_name ?? $resource->division->name;
                    }
                });
                break;
            case 'skill':
                $groupedResources = $resources
                    ->map(function ($user) {
                        $userWithSkillName = clone $user;
                        $userWithSkillName->skills = array_column($user->skills, 'skill');
                        return $userWithSkillName;
                    })
                    ->groupBy('skills');
                break;
            default:
                $groupedResources = [];
        }
        return $groupedResources;
    }

    public function mapResourceAllocationBySkill($resources, $allocations, $date, $holiday)
    {
        $resources->map(function ($resource) use ($allocations, $date, $holiday) {
            $allocates = array_filter(
                $allocations,
                fn ($allocation) => $allocation->user_id == $resource->user_id
            );
            $resource->allocate = array_reduce(
                $allocates,
                function ($prev, $item) use ($date, $holiday) {
                    $allocatedOfMonth = $this->calManMonthSingleMonth(
                        $item->start_date,
                        $item->end_date,
                        $item->allocation,
                        $date,
                        $holiday
                    );

                    return $prev + $allocatedOfMonth / 100;
                },
                0
            );
        });
        $resources = $resources
            ->groupBy(fn ($resource) => $resource->skills)
            ->map(function ($members) {
                $allocation = ['free' => 0, 'allocated' => 0, 'overload' => 0];
                $members->each(function ($member) use (&$allocation) {
                    if ($member->allocate < 1) {
                        $allocation['free'] += 1 - $member->allocate;
                        $allocation['allocated'] += $member->allocate;
                    }
                    if ($member->allocate >= 1) {
                        $allocation['allocated'] += 1;
                        $allocation['overload'] += $member->allocate - 1;
                    }
                });

                return $allocation;
            });

        return $resources;
    }

    public function mapProjectResourceAllocation($allocations, $date, $holiday)
    {
        return collect($allocations)->map(function ($item) use ($date, $holiday) {
            $allocatedOfMonth = $this->calManMonthSingleMonth(
                $item->start_date,
                $item->end_date,
                $item->allocation,
                $date,
                $holiday
            );
            $item->allocatedOfMonth = $allocatedOfMonth / 100;

            return $item;
        });
    }

    public function mapProjectResourceAllocationWithOnboardDate($allocations, $date, $holiday)
    {
        return collect($allocations)->map(function ($item) use ($date, $holiday) {
            $allocatedOfMonth = $this->calManMonthSingleMonthWithOnboardDate(
                $item->start_date,
                $item->end_date,
                $item->allocation,
                $date,
                $item->onboard_date,
                $holiday
            );
            $item->allocatedOfMonth = $allocatedOfMonth / 100;

            return $item;
        });
    }

    public function getUserGroup($args)
    {
        return $this->__getUserGroup($args);
    }

    public function calBusyRateByManDay($allocates, $from, $to, $holiday)
    {
        $allocates = $allocates->map(function ($allocate) use ($from, $to, $holiday) {
            $from = Carbon::createFromFormat('Y-m-d', $from);
            $to = Carbon::createFromFormat('Y-m-d', $to);
            $days = $this->numberOfWorkingDays($from->format('Y-m-d'), $to->format('Y-m-d'), $holiday);

            if ($from->gte($allocate->start_date)) $startDate = $from;
            else $startDate = Carbon::createFromFormat('Y-m-d', $allocate->start_date);

            if (!empty($allocate->onboard_date) && $from->lte($allocate->onboard_date)) {
                $days = $this->numberOfWorkingDays($allocate->onboard_date, $to->format('Y-m-d'), $holiday);
            }

            if ($to->lte($allocate->end_date)) $endDate = $to;
            else $endDate = Carbon::createFromFormat('Y-m-d', $allocate->end_date);

            $actualWorkingDays = $this->numberOfWorkingDays($startDate->format('Y-m-d'), $endDate->format('Y-m-d'), $holiday);
            if ($actualWorkingDays > $days) $actualWorkingDays = $days;
            $allocate->effort = $allocate->allocation * $actualWorkingDays;
            $allocate->total_day = $days;

            return $allocate;
        });
        $allocates = $allocates->groupBy('user_id')
            ->map(function ($allocate) {
                $effort = new stdClass();
                $effort->totalEffort = $allocate->reduce(fn ($prev, $allocate) => $prev + $allocate->effort, 0);
                $effort->totalDays = $allocate->first()->total_day;
                return $effort;
            });
        $result['total_effort'] = $allocates->reduce(fn ($prev, $allocate) => $prev + $allocate->totalEffort, 0);
        $result['total_day'] = $allocates->reduce(fn ($prev, $allocate) => $prev + $allocate->totalDays, 0);
        return $result;
    }

    public function calBusyRateUserLeave($userLeaveAllocate, $from, $to, $holiday)
    {
        /**
         * số ngày tính effort = Số ngày trên filter - tổng số ngày nghỉ phép
         * vì trong khoản thời gian filter user có thể đc allocate nhiều lần nên sẽ group lại để tính trung bình
         * cách tính busy rate: (số ngày làm viêc thực tế) * % allocate / tổng số ngày
         * khi có nhiều user dùng công thức bên trên: tử số + tử số / mẫu số + mẫu số
         */
        $userEffort = $userLeaveAllocate
            ->map(function ($allocation) use ($holiday, $from, $to) {
                $daysOff = $allocation->leave->reduce(function ($prev, $leave) use ($holiday) {
                    $daysOff = $this->numberOfWorkingDays($leave->start_date, $leave->end_date, $holiday);
                    return $prev + $daysOff;
                }, 0);
                $daysFilterRange = $this->numberOfWorkingDays($from, $to, $holiday);
                $allocation->total_day = $daysFilterRange - $daysOff;
                /**
                 * nếu ngày allocation > ngày filter -> đưa khoảng thời gian allocate về bằng với filter
                 * nếu user nghỉ giữa thời gian đc allocate -> allocate end_date = checkout date
                 */
                if (Carbon::createFromDate($from)->gte($allocation->start_date)) $startDate = $from;
                else $startDate = Carbon::createFromFormat('Y-m-d', $allocation->start_date);

                if (Carbon::createFromDate($to)->lte($allocation->end_date)) $endDate = $to;
                else $endDate = Carbon::createFromFormat('Y-m-d', $allocation->end_date);
                if ($allocation->leave->count() == 1) {
                    $leave = $allocation->leave->first();
                    if (isset($leave->checkout_date) && Carbon::createFromDate($leave->checkout_date)->between($startDate, $endDate)) {
                        $endDate = $leave->checkout_date;
                    };
                }
                $actualWorkingDays = $this->numberOfWorkingDays($startDate, $endDate, $holiday);
                $allocation->effort = $allocation->allocation * $actualWorkingDays;
                return $allocation;
            })
            ->groupBy('user_id')
            ->map(function ($allocate) {
                $effort = new stdClass();
                $effort->totalEffort = $allocate->reduce(fn ($prev, $allocate) => $prev + $allocate->effort, 0);
                $effort->totalDays = $allocate->first()->total_day;
                return $effort;
            });
        $result['total_effort'] = $userEffort->reduce(fn ($prev, $allocate) => $prev + $allocate->totalEffort, 0);
        $result['total_day'] = $userEffort->reduce(fn ($prev, $allocate) => $prev + $allocate->totalDays, 0);
        return $result;
    }

    public function filterResourceWithCheckoutDateAndUnpaidLeave($resources, $userUnpaidLeave, $args)
    {
        /**
         * Loại nếu ngày ký hợp đồng của user nằm sau khoảng thời gian filter
         * Loại nếu ngày nghỉ việc của user nằm trước khoảng thời gian filter
         * Trả về user nếu check user ko có nghỉ phép
         * Loại user nếu tất cả khoảng thời gian nghỉ phép đều bao ngoài thời gian filter
         */
        $resources = $resources->filter(function ($user) use ($args, $userUnpaidLeave) {
            $checkUserInvalid = true;
            if (isset($user->onboard_date) && Carbon::createFromDate($args['to'])->lt($user->onboard_date)) {
                $checkUserInvalid = false;
            }
            if (isset($user->checkout_date) && $user->status == 0 && Carbon::createFromDate($user->checkout_date)->lt($args['from'])) {
                $checkUserInvalid = false;
            }
            if (!isset($user->checkout_date) && $user->status == 0) {
                $checkUserInvalid = false;
            }
            if ($userUnpaidLeave->isNotEmpty()) {
                $userLeave = $userUnpaidLeave->filter(fn ($unpaidLeave) => $user->user_id == $unpaidLeave->user_id);
                if ($userLeave->isNotEmpty()) {
                    $checkUserInvalid = !$userLeave->some(function ($unpaidLeave) use ($args) {
                        $startLeave = Carbon::createFromDate($unpaidLeave->start_date);
                        $endLeave = Carbon::createFromDate($unpaidLeave->end_date);
                        return ($startLeave->lte($args['from']) && $endLeave->gte($args['to']));
                    });
                }
            }

            return $checkUserInvalid;
        })->values();

        return $resources;
    }

    public function mapAllocateUnpaidLeave($resources, $allocations, $userUnpaidLeave, $holiday)
    {
        return collect($allocations)->map(function ($allocate) use ($resources, $userUnpaidLeave, $holiday) {
            $user = $resources->first(fn ($user) => $user->user_id == $allocate->user_id);
            $allocate->onboard_date = $user->onboard_date ?? null;
            if ($userUnpaidLeave->isNotEmpty()) {
                $unpaidLeave = $userUnpaidLeave->filter(fn ($user) => $user->user_id == $allocate->user_id);
                $allocate->days_leave = $unpaidLeave->reduce(function ($prev, $leave) use ($holiday) {
                    $daysLeave = $this->numberOfWorkingDays($leave->start_date, $leave->end_date, $holiday);
                    return $prev + $daysLeave;
                }, 0);
            }

            return $allocate;
        })->toArray();
    }

    public function calLogWorkEffort($dailyReports, $calculateToday = false)
    {
        $applyDate = $calculateToday ? Carbon::today() : Carbon::yesterday();
        $totalDailyReport = 0;
        foreach ($dailyReports as $report) {
            if (Carbon::createFromDate($report->work_date)->lte($applyDate)) {
                $totalDailyReport += ($report->actual_time ?? 0);
            }
        }

        return $totalDailyReport;
    }

    public function calLogWorkEffortWithConfficient($dailyReports)
    {
        $totalDailyReport = 0;
        foreach ($dailyReports as $report) {
            if (Carbon::createFromDate($report->work_date)->lte(Carbon::yesterday())) {
                $coefficient = !empty($report->coefficient) ? $report->coefficient : 1;
                $totalDailyReport += ($report->actual_time * $coefficient);
            }
        }

        return $totalDailyReport;
    }

    public function calCalendarEffort($allocates, $from, $to, $holiday, $unpaidLeave)
    {
        $allocates = $allocates->unique('id');
        return $allocates->transform(function ($allocate) use ($from, $to, $holiday, $unpaidLeave) {
            $now = Carbon::today();
            $startDate = max($allocate->start_date, $from);
            $endDate = min($allocate->end_date, $to);
            if ($now->between($startDate, $endDate)) $startDate = $now->format('Y-m-d');
            if (Carbon::createFromDate($to)->between($startDate, $endDate)) $endDate = $to;
            $workingDays = $this->getNumberOfWorkingDays($startDate, $endDate, $holiday, collect($unpaidLeave));
            /**
             * nếu thời gian allocate nhỏ hơn ngày hôm nay không tính effort theo allocate nữa
             */
            if ($now->gt($endDate)) $allocate->effort = 0;
            else $allocate->effort = $workingDays * 8 * $allocate->allocation / 100;
            return $allocate;
        })->reduce(fn ($prev, $item) => $prev + $item->effort, 0);
    }

    public function calAllocateEffort($allocates, $from, $to, $holiday, $unpaidLeave = [])
    {
        return $allocates->transform(function ($allocate) use ($from, $to, $holiday, $unpaidLeave) {
            $startDate = max($allocate->start_date, $from);
            $endDate = min($allocate->end_date, $to);
            if (Carbon::createFromDate($to)->between($startDate, $endDate)) $endDate = $to;
            $workingDays = $this->getNumberOfWorkingDays($startDate, $endDate, $holiday, collect($unpaidLeave));
            $allocate->effort = $workingDays * 8 * $allocate->allocation / 100;
            return $allocate;
        })->reduce(fn ($prev, $item) => $prev + $item->effort, 0);
    }

    public function calAllocateEffortWithCoefficient($allocates, $from, $to, $holiday, $unpaidLeave)
    {
        $effort = $allocates->transform(function ($allocate) use ($from, $to, $holiday, $unpaidLeave) {
            $startDate = max($allocate->start_date, $from);
            $endDate = min($allocate->end_date, $to);
            if (Carbon::createFromDate($to)->between($startDate, $endDate)) $endDate = $to;
            $workingDays = $this->getNumberOfWorkingDays($startDate, $endDate, $holiday, collect($unpaidLeave));
            $coefficient = !empty($allocate->coefficient) ? $allocate->coefficient : 1;
            $allocate->effort = $workingDays * 8 * $allocate->allocation * $coefficient / 100;
            return $allocate;
        })->reduce(fn ($prev, $item) => $prev + $item->effort, 0);
        return $effort;
    }

    public function calCalendarEffortBeforeV2ApplyDate($allocates, $from, $to, $holiday, $unpaidLeave, $v2ApplyDate)
    {
        return $allocates->transform(function ($allocate) use ($from, $to, $holiday, $unpaidLeave, $v2ApplyDate) {
            $startDate = max($allocate->start_date, $from);
            $endDate = min($allocate->end_date, $v2ApplyDate);
            $endDate = min($endDate, $to);

            if ($this->datesOverlap($startDate, $endDate, $from, $to) == 0) {
                $allocate->effort = 0;
                return $allocate;
            }

            $workingDays = $this->getNumberOfWorkingDays($startDate, $endDate, $holiday, collect($unpaidLeave));
            $allocate->effort = $workingDays * 8 * $allocate->allocation / 100;
            return $allocate;
        })->reduce(fn ($prev, $item) => $prev + $item->effort, 0);
    }

    private function datesOverlap($start_one, $end_one, $start_two, $end_two)
    {

        if ($start_one <= $end_two && $end_one >= $start_two) { //If the dates overlap
            $endDate = new DateTime(min($end_one, $end_two));
            $startDate = new DateTime(max($start_two, $start_one));
            return $endDate->diff($startDate)->days + 1; //return how many days overlap
        }

        return 0; //Return 0 if there is no overlap
    }

    public function calBusyRate($resources, $from, $to, $holiday, $v2ApplyDate, $onlyAllocate)
    {
        $totalEffort = 0;
        $totalWorkingHours = 0;
        $resources->transform(function ($resource) use ($from, $to, $holiday, $v2ApplyDate, $onlyAllocate) {
            $logWorkEffort = 0;
            $calendarEffort = 0;
            $effortBeforeV2ApplyDate = 0;
            $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday, $resource->unpaid_leave ?? [], $resource->onboard_date ?? null, $resource->checkout_date ?? null);
            if (
                isset($resource->daily_reports)
                && Carbon::today()->gt($from)
                && Carbon::parse($v2ApplyDate)->lt($to)
            ) {
                $logWorkEffort = $this->calLogWorkEffort($resource->daily_reports);
            }
            if (
                isset($resource->allocates)
                && (Carbon::parse($v2ApplyDate)->gte($to) || Carbon::parse($v2ApplyDate)->between($from, $to))
            ) {
                $effortBeforeV2ApplyDate = $this->calCalendarEffortBeforeV2ApplyDate(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave, $v2ApplyDate);
            }
            if (
                isset($resource->allocates)
                && (Carbon::parse($v2ApplyDate)->lt($from) || Carbon::parse($v2ApplyDate)->between($from, $to))
            ) {
                $calendarEffort = $this->calCalendarEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave);
            }
            if ($onlyAllocate == true || Carbon::parse($v2ApplyDate)->gt($to)) $resource->paid_leave = 0;
            $effort = $calendarEffort + $logWorkEffort + $effortBeforeV2ApplyDate - $resource->paid_leave ?? 0;
            if ((int)$effort < 0) $effort = 0;
            return collect([
                'effort' => $effort ?? 0,
                'working_hours' => $actualWorkingDays * 8 - $resource->paid_leave ?? 0
            ]);
        })->each(function ($resource) use (&$totalEffort, &$totalWorkingHours) {
            $totalEffort += $resource['effort'];
            $totalWorkingHours += $resource['working_hours'];
        });
        if ($totalWorkingHours == 0) return 0;
        return $totalEffort / $totalWorkingHours * 100;
    }

    public function mapDivisionEffort($resources, $from, $to, $holiday)
    {
        $resources = $resources->transform(function ($resource) use ($from, $to, $holiday) {
            $approveHours = 0;
            $waitingHours = 0;
            $allocateHours = 0;
            $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday ?? [], [], null, null);
            if ($actualWorkingDays == 0) return false;

            $workingHours = $actualWorkingDays * 8;
            $calculateToday = true;
            $approveHours = $this->calLogWorkEffort($resource->approved_reports ?? [], $calculateToday);
            $waitingHours = $this->calLogWorkEffort($resource->waiting_reports ?? [], $calculateToday);
            if (isset($resource->allocates)) {
                $allocates = collect($resource->allocates)->map(function ($allocate) {
                    if (Carbon::parse($allocate->end_date)->gt(Carbon::today())) $allocate->end_date = Carbon::today()->format('Y-m-d');
                    return $allocate;
                });
                $allocateHours = $this->calAllocateEffort($allocates, $from, $to, $holiday ?? [], $resource->unpaid_leave ?? []);
            }

            $resource->approved_effort = $approveHours / $workingHours;
            $resource->waiting_effort = $waitingHours / $workingHours;
            $resource->allocate_effort = $allocateHours / $workingHours;
            unset($resource->daily_reports, $resource->allocates, $resource->skills, $resource->approved_reports, $resource->waiting_reports, $resource->level, $resource->position);
            return  $resource;
        })
            ->filter()
            ->groupBy('division.name')
            ->map(function ($members, $key) {
                $waitingEffort = $members->reduce(fn ($prev, $member) => $prev + $member->waiting_effort ?? 0, 0);
                $approvedEffort = $members->reduce(fn ($prev, $member) => $prev + $member->approved_effort ?? 0, 0);
                $allocateEffort = $members->reduce(fn ($prev, $member) => $prev + $member->allocate_effort ?? 0, 0);
                $division = [
                    [
                        'division' => $key,
                        'group' => 'report',
                        'type' => 'pending',
                        'value' => round($waitingEffort, 2)
                    ],
                    [
                        'division' => $key,
                        'group' => 'report',
                        'type' => 'success',
                        'value' => round($approvedEffort, 2)
                    ],                    [
                        'division' => $key,
                        'group' => 'allocate',
                        'type' => 'allocate',
                        'value' => round($allocateEffort, 2)
                    ]
                ];
                return $division;
            })->flatten(1);
        return $resources;
    }

    public function mapTeamEffort($resources, $from, $to, $holiday)
    {
        $resources = $resources->transform(function ($resource) use ($from, $to, $holiday) {
            $approveHours = 0;
            $waitingHours = 0;
            $allocateHours = 0;
            $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday ?? [], [], null, null);
            if ($actualWorkingDays == 0) return false;

            $workingHours = $actualWorkingDays * 8;
            $calculateToday = true;
            $approveHours = $this->calLogWorkEffort($resource->approved_reports ?? [], $calculateToday);
            $waitingHours = $this->calLogWorkEffort($resource->waiting_reports ?? [], $calculateToday);
            if (isset($resource->allocates)) {
                $allocates = collect($resource->allocates)->map(function ($allocate) {
                    if (Carbon::parse($allocate->end_date)->gt(Carbon::today())) $allocate->end_date = Carbon::today()->format('Y-m-d');
                    return $allocate;
                });
                $allocateHours = $this->calAllocateEffort($allocates, $from, $to, $holiday ?? [], $resource->unpaid_leave ?? []);
            }

            $resource->approved_effort = $approveHours / $workingHours;
            $resource->waiting_effort = $waitingHours / $workingHours;
            $resource->allocate_effort = $allocateHours / $workingHours;
            unset($resource->daily_reports, $resource->allocates, $resource->skills, $resource->approved_reports, $resource->waiting_reports, $resource->level, $resource->position);
            return  $resource;
        })
            ->filter()
            ->groupBy('team.name')
            ->map(function ($members, $key) {
                $waitingEffort = $members->reduce(fn ($prev, $member) => $prev + $member->waiting_effort ?? 0, 0);
                $approvedEffort = $members->reduce(fn ($prev, $member) => $prev + $member->approved_effort ?? 0, 0);
                $allocateEffort = $members->reduce(fn ($prev, $member) => $prev + $member->allocate_effort ?? 0, 0);
                $team = [
                    [
                        'team' => $key,
                        'group' => 'report',
                        'type' => 'pending',
                        'value' => round($waitingEffort, 2)
                    ],
                    [
                        'team' => $key,
                        'group' => 'report',
                        'type' => 'success',
                        'value' => round($approvedEffort, 2)
                    ],                    [
                        'team' => $key,
                        'group' => 'allocate',
                        'type' => 'allocate',
                        'value' => round($allocateEffort, 2)
                    ]
                ];
                return $team;
            })->flatten(1);
        return $resources;
    }

    public function mapProjectEffort($projects, $from, $to, $holiday)
    {
        $projects = $projects->transform(function ($project) use ($from, $to, $holiday) {
            $approveHours = 0;
            $waitingHours = 0;
            $allocateHours = 0;
            $calculateToday = true;
            $approveHours = $this->calLogWorkEffort($project->approved_reports ?? [], $calculateToday);
            $waitingHours = $this->calLogWorkEffort($project->waiting_reports ?? [], $calculateToday);
            if (isset($project->allocates)) {
                $allocates = collect($project->allocates)->map(function ($allocate) {
                    if (Carbon::parse($allocate->end_date)->gt(Carbon::today())) $allocate->end_date = Carbon::today()->format('Y-m-d');
                    return $allocate;
                });
                $allocateHours = $this->calAllocateEffort($allocates, $from, $to, $holiday ?? [], []);
            }
            $project->approved_hours = $approveHours;
            $project->waiting_hours = $waitingHours;
            $project->allocate_hours = $allocateHours;
            unset($project->daily_reports, $project->allocates, $project->skills, $project->approved_reports, $project->waiting_reports, $project->level, $project->position);
            return  $project;
        })
            ->filter()
            ->groupBy('name')
            ->map(function ($members, $key) {
                $waitingEffort = $members->reduce(fn ($prev, $member) => $prev + $member->waiting_hours ?? 0, 0);
                $approvedEffort = $members->reduce(fn ($prev, $member) => $prev + $member->approved_hours ?? 0, 0);
                $allocateEffort = $members->reduce(fn ($prev, $member) => $prev + $member->allocate_hours ?? 0, 0);
                $project = [
                    [
                        'project' => $key,
                        'group' => 'report',
                        'type' => 'pending',
                        'value' => round($waitingEffort, 2)
                    ],
                    [
                        'project' => $key,
                        'group' => 'report',
                        'type' => 'success',
                        'value' => round($approvedEffort, 2)
                    ],                    [
                        'project' => $key,
                        'group' => 'allocate',
                        'type' => 'allocate',
                        'value' => round($allocateEffort, 2)
                    ]
                ];
                return $project;
            })->flatten(1);
        return $projects;
    }

    public function getContractTypes($args = [])
    {
        $contractTypes = $this->__getContractType($args) ?? [];

        return collect($contractTypes)
            ->map(
                fn ($contractType) => [
                    'id' => $contractType->id,
                    'name' => !empty(EContractType::convertContractType($contractType->id))
                        ? EContractType::convertContractType($contractType->id)
                        : $contractType->name
                ]
            );
    }

    public function calculateResourceEffort($resources, $holiday, $v2ApplyDate, $startDate, $endDate)
    {
        $resources->transform(function ($resource) use ($holiday, $v2ApplyDate, $startDate, $endDate) {
            $months = collect([]);
            $approveHours = 0;
            $waitingHours = 0;
            $allocateHours = 0;
            $calculateToday = true;
            $approveHours = $this->calLogWorkEffort($resource->approved_reports ?? [], $calculateToday);
            $waitingHours = $this->calLogWorkEffort($resource->waiting_reports ?? [], $calculateToday);
            if (isset($resource->allocates)) {
                $allocateArr = collect([]);
                collect($resource->allocates)->each(function ($allocate) use (&$allocateArr) {
                    $tmpAllocates = new stdClass();
                    $tmpAllocates->start_date = $allocate->start_date;
                    $tmpAllocates->end_date = $allocate->end_date;
                    $tmpAllocates->allocation = $allocate->allocation;
                    $tmpAllocates->coefficients = $allocate->coefficient;
                    if (Carbon::parse($tmpAllocates->end_date)->gt(Carbon::today())) {
                        $tmpAllocates->end_date = Carbon::today()->format('Y-m-d');
                    }
                    $allocateArr->push($tmpAllocates);
                });
                $allocateHours = $this->calAllocateEffort($allocateArr, $startDate, $endDate, $holiday ?? [], $resource->unpaid_leave ?? []);
            }
            $resource->approved_hours = $approveHours;
            $resource->waiting_hours = $waitingHours;
            $resource->allocated_hours = $allocateHours;

            foreach ($resource->allocates as $allocate) {
                $monthsBetweenAllocate = $this->getMonthsBetween($allocate->start_date, $allocate->end_date);
                $months->push($monthsBetweenAllocate);
            }
            foreach ($resource->daily_reports as $report) {
                $workTime = Carbon::parse($report->work_date);
                $months->push($workTime->format('Y-m-d'));
            }

            $months = $months->flatten()->map(fn ($month) => substr($month, 0, -3))->unique();
            $months->each(function ($month) use ($resource, $holiday, $v2ApplyDate, $startDate, $endDate) {
                $tmpMonth = Carbon::parse($month);
                $from = $tmpMonth->startOfMonth()->format('Y-m-d');
                $to = $tmpMonth->endOfMonth()->format('Y-m-d');
                $logWorkHours = 0;
                $calendarEffort = 0;
                $allocateEffort = 0;
                $logWorkEffort = 0;
                $effortBeforeV2ApplyDate = 0;
                $leaveHours = 0;
                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->gte($to) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $effortBeforeV2ApplyDate = $this->calCalendarEffortBeforeV2ApplyDate(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave, $v2ApplyDate);
                }
                $actualWorkingDays = $this->getNumberOfWorkingDays($from, $to, $holiday, [], null, null);
                if (isset($resource->reports_of_month[$month]) && Carbon::today()->gt($from) && Carbon::parse($v2ApplyDate)->lt($to)) {
                    $dailyReports = $resource->reports_of_month[$month];
                    $calculateToday = true;
                    $logWorkHours = $this->calLogWorkEffort($dailyReports, $calculateToday);
                }
                if (isset($resource->days_leave) && $resource->days_leave->isNotEmpty()) {
                    $resource->days_leave->each(function ($dayLeave) use ($from, $to, &$leaveHours) {
                        if (Carbon::parse($dayLeave->created_at)->between($from, $to)) {
                            $shifts = explode(',', $dayLeave->paid_leave);
                            foreach ($shifts as $shift) {
                                if ($shift != 0) $leaveHours += 2;
                            };
                        }
                    });
                }
                if (
                    isset($resource->allocates)
                    && (Carbon::parse($v2ApplyDate)->lt($from) || Carbon::parse($v2ApplyDate)->between($from, $to))
                ) {
                    $calendarEffort = $this->calCalendarEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);
                }
                if (isset($resource->allocates)) $allocateEffort = $this->calAllocateEffort(collect($resource->allocates), $from, $to, $holiday, $resource->unpaid_leave ?? []);
                $totalWorkingHours = $actualWorkingDays * 8 - $leaveHours ?? 0;
                if ($actualWorkingDays == 0) $actualWorkingDays = 1;
                if ($totalWorkingHours == 0) $totalWorkingHours = 1;
                $totalEffort = $calendarEffort + $logWorkHours + $effortBeforeV2ApplyDate - $leaveHours ?? 0;
                $actualEffort = $totalEffort / $totalWorkingHours * 100;
                $logWorkEffort = $logWorkHours / $totalWorkingHours * 100;
                $resource->month_schedule[] = [
                    'title' => date('Y', strtotime($month)) . ' - ' . 'Tháng ' . date('m', strtotime($month)),
                    'actual_effort' => $actualEffort,
                    'allocate_effort' => ($allocateEffort / ($actualWorkingDays * 8)) * 100,
                    'total_working_hour' => $actualWorkingDays * 8,
                    'total_daily_report' => $logWorkHours,
                    'daily_report_effort' => $logWorkEffort,
                    'start_date' => $from,
                    'end_date' => $to,
                ];
            });
            $differentHours = $allocateHours - $approveHours;
            if ($differentHours < 0) $resource->log_work_status = EDailyReport::OVERLOAD_STATUS;
            elseif ($differentHours == 0) $resource->log_work_status = EDailyReport::COMPLETE_STATUS;
            elseif ($differentHours > 0) $resource->log_work_status = EDailyReport::INCOMPLETE_STATUS;
            $resource->diff_hours = abs($differentHours);
            unset(
                $resource->daily_reports,
                $resource->reports_of_month,
                $resource->reports_of_week,
                $resource->days_leave,
                $resource->skills,
                $resource->level,
                $resource->position,
                $resource->approved_reports,
                $resource->waiting_reports

            );
            return $resource;
        });

        return $resources;
    }

    public function calculateDivisionMetrics($resources, $holiday, $startDate, $endDate)
    {
        $resources->transform(function ($resource) use ($holiday, $startDate, $endDate) {
            $approveHours = 0;
            $waitingHours = 0;
            $allocateHours = 0;
            $calculateToday = true;
            $approveHours = $this->calLogWorkEffort($resource->approved_reports ?? [], $calculateToday);
            $waitingHours = $this->calLogWorkEffort($resource->waiting_reports ?? [], $calculateToday);
            if (isset($resource->allocates)) {
                $allocates = collect($resource->allocates)->map(function ($allocate) {
                    if (Carbon::parse($allocate->end_date)->gt(Carbon::today())) $allocate->end_date = Carbon::today()->format('Y-m-d');
                    return $allocate;
                });
                $allocateHours = $this->calAllocateEffort($allocates, $startDate, $endDate, $holiday ?? [], $resource->unpaid_leave ?? []);
            }
            $actualWorkingDays = $this->getNumberOfWorkingDays($startDate, $endDate, $holiday ?? [], [], null, null);
            $workingHours = $actualWorkingDays * 8;

            if ($actualWorkingDays == 0 || $workingHours == 0) return false;

            $resource->approved_effort = $approveHours / $workingHours;
            $resource->waiting_effort = $waitingHours / $workingHours;
            $resource->allocate_effort = $allocateHours / $workingHours;

            $differentEffort =  $resource->allocate_effort - $resource->approved_effort;
            if ($differentEffort < 0) $resource->log_work_status = EDailyReport::OVERLOAD_STATUS;
            elseif ($differentEffort == 0) $resource->log_work_status = EDailyReport::COMPLETE_STATUS;
            elseif ($differentEffort > 0) $resource->log_work_status = EDailyReport::INCOMPLETE_STATUS;
            $resource->diff_effort = $differentEffort;

            return $resource;
        });
        $waitingEffort = 0;
        $approvedEffort = 0;
        $allocateEffort = 0;
        $differentEffort = 0;
        foreach ($resources as $key => $resource) {
            $waitingEffort += $resource->waiting_effort;
            $approvedEffort += $resource->approved_effort;
            $allocateEffort += $resource->allocate_effort;
            $differentEffort += $resource->diff_effort;
        }

        return [
            'waiting_effort' => round($waitingEffort, 2),
            'approved_effort' => round($approvedEffort, 2),
            'allocate_effort' => round($allocateEffort, 2),
            'different_effort' => round($differentEffort, 2),
            'total_effort' => count($resources)
        ];
    }
}
