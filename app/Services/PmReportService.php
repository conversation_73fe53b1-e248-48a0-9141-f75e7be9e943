<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;

class PmReportService
{
    use ProjectServiceSupport;

    public function getListPmReport($args, $projectId)
    {
        return $this->__getListPmReport($args, $projectId);
    }

    public function store($args, $projectId)
    {
        return $this->__createPmReport($args, $projectId);
    }

    public function update($args, $projectId, $id)
    {
        return $this->__updatePmReport($args, $projectId, $id);
    }

    public function show($projectId, $id)
    {
        return $this->__showPmReport($projectId, $id);
    }

    public function destroy($projectId, $id)
    {
        return $this->__deletePmReport(
            $projectId,
            $id
        );
    }

    public function getDateReported($id)
    {
        return $this->__getDateReported($id);
    }
}
