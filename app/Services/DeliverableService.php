<?php

namespace App\Services;

use App\Traits\ProjectServiceSupport;

class DeliverableService
{
    use ProjectServiceSupport;

    public function __construct()
    {
    }

    public function store($args, $projectId, $stageId)
    {
        return $this->__createDeliverable($args, $projectId, $stageId);
    }

    public function update($args, $projectId, $stageId, $id)
    {
        return $this->__updateDeliverable($args, $projectId, $stageId, $id);
    }

    public function destroy($args, $projectId, $stageId, $id)
    {
        return $this->__deleteDeliverable($args, $projectId, $stageId, $id);
    }
}
