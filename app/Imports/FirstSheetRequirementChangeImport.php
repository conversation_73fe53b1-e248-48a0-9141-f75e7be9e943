<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithStartRow;

class FirstSheetRequirementChangeImport implements ToCollection, WithHeadingRow, SkipsEmptyRows, WithStartRow
{
    /**
     * @param  Collection  $collection
     */
    public function collection(Collection $collection)
    {
    }

    public function startRow(): int
    {
        return 2;
    }
}
