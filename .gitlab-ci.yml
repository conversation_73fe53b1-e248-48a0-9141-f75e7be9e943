stages:
  - build
  - deploy

deploy_test:
  stage: deploy
  image: alpine
  tags:
    - $RUNNER_TAG
  only:
    - dev
  script:
    - set -e
    - apk add --no-cache rsync openssh
    - mkdir -p ~/.ssh
    - echo "$TEST_ENV" > .env
    - ssh-keyscan -H $TEST_HOST >> ~/.ssh/known_hosts
    - echo "$TEST_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - rsync -avz --delete --include="storage/logs/**" --exclude=".git" --exclude="vendor" --exclude="storage/*" . ubuntu@$TEST_HOST:/var/www/acms_project_presentation
    - ssh -i ~/.ssh/id_rsa ubuntu@$TEST_HOST "
      cd /var/www/acms_project_presentation
      && docker-compose exec -T app composer install
      && docker-compose exec -T app php artisan optimize:clear"

build:
  stage: build
  image: docker:rc-dind-rootless
  services:
    - docker:dind
  variables:
    API_IMAGE_TAG: $CI_REGISTRY_IMAGE/$CI_COMMIT_REF_NAME:api-$CI_COMMIT_SHA
    API_IMAGE_TAG_BEFORE: $CI_REGISTRY_IMAGE/$CONTAINER_REGISTRY_IMAGE
    DOCKER_TLS_CERTDIR: ""
  tags:
    - $RUNNER_TAG
  only:
    - stg
    - prod
  script:
    - docker login git.amela.vn:5050 -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD"
    - docker build --build-arg BASE_IMAGE=$API_IMAGE_TAG_BEFORE -t $API_IMAGE_TAG -f docker/Dockerfile .
    - docker push $API_IMAGE_TAG

deploy_k8s:
  stage: deploy
  image: docker:rc-dind-rootless
  tags:
    - $RUNNER_TAG
  needs:
    - build
  only:
    - stg
    - prod
  script:
    - mkdir -p ~/.ssh
    - echo "${CI_COMMIT_REF_NAME}" | grep "^stg" > /dev/null && export ENV_NAME="stg"
    - echo "${CI_COMMIT_REF_NAME}" | grep "^prod" > /dev/null && export ENV_NAME="prod"
    - |
      if [ "$ENV_NAME" = "stg" ]; then
        export KEY=$STG_KEY
        export HOST=$STG_HOST
        export DEPLOYMENT_FOLDER="test-deployment"
        export CRONJOB_FOLDER="staging-cronjob"
      fi
    - |
      if [ "$ENV_NAME" = "prod" ]; then
        export KEY=$PROD_KEY
        export HOST=$PROD_HOST
        export DEPLOYMENT_FOLDER="prod-deployment"
        export CRONJOB_FOLDER="cronjob"
      fi
    - echo "$KEY" > ~/.ssh/id_rsa
    - chmod 0700 ~/.ssh
    - chmod 0600 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa root@$HOST "
      sed -i 's/\b[0-9a-f]\{5,40\}\b/'"$CI_COMMIT_SHA"'/g' '/opt/eks/${DEPLOYMENT_FOLDER}/pod-project-presentation-api.yml'
      && kubectl apply -f /opt/eks/${DEPLOYMENT_FOLDER}/pod-project-presentation-api.yml
      && sed -i 's/\b[0-9a-f]\{5,40\}\b/'"$CI_COMMIT_SHA"'/g' '/opt/eks/${CRONJOB_FOLDER}/daily-report-not-enough-alert.yaml'
      && kubectl apply -f /opt/eks/${CRONJOB_FOLDER}/daily-report-not-enough-alert.yaml
      "
