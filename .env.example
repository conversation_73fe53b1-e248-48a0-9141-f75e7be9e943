APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=daily
LOG_CHANNEL_API=daily
LOG_CHANNEL_ERROR=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
ENABLE_ERROR_LOG=false

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=
REDIS_QUEUE=

MONGO_DB_HOST=
MONGO_DB_PORT=
MONGO_DB_DATABASE=
MONGO_DB_USERNAME=
MONGO_DB_PASSWORD=
MONGO_BASIC_AUTH_USERNAME=
MONGO_BASIC_AUTH_PASSWORD=

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_URL=
FILESYSTEM_DRIVER=local
LINK_S3=
FOLDER=images/
VISIBILITY=public
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=omg12%&sadfkdasfks#$%^%-564613
JWT_ALGO=HS256

CACHE_EXPIRE_SECOND=3600

LINK_USER_SERVICE=https://acms-user-api.test4.amelacorp.com/api/
LINK_ASSETS_SERVICE=https://acms-assets-api.test4.amelacorp.com/api/
LINK_HR_SERVICE=https://acms-hr-api.test4.amelacorp.com/api/
LINK_REQUEST=https://bwf-api.test3.amelacorp.com/api/
LINK_PROJECT_SERVICE=https://acms-project-service.test4.amelacorp.com/api/
LINK_DIVISION_PRESENTATION=https://acms-division-api.test4.amelacorp.com/api/
NOTIFICATION_DOMAIN=https://acms-notification-api.test4.amelacorp.com

AMS_LINK=https://ams.test.amela.vn/api/
AMS_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIxLCJpYXQiOjE2NzAzMDA2OTUsIm5iZiI6MTY3MDMwMDY5NSwiZXhwIjo0ODIzOTAwNjk1LCJpc3MiOiJhbXMudGVzdC5hbWVsYS52biJ9.VGaNHLTDZaB1HUzswUBhur57raME87n82mxAIHAev9c

MS_LOGIN_API="https://login.microsoftonline.com/"
MS_REFRESH_TOKEN="********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
MS_CLIENT_ID=c1a01580-6255-4ecb-9659-f88ec9685d3b
MS_CLIENT_SECRET=****************************************
MS_TENANT_ID=94675deb-b6ed-4d28-8d46-e477fdb4a97d
MS_AUTH_TENANT=94675deb-b6ed-4d28-8d46-e477fdb4a97d
MS_GRAPH_USER_SCOPES=https://graph.microsoft.com/.default

TEAM_WEBHOOK_URL=https://amelavn.webhook.office.com/webhookb2/f6893e8e-d471-48c9-9b02-5cff6a00b702@94675deb-b6ed-4d28-8d46-e477fdb4a97d/IncomingWebhook/85a615eecbe1441ba266de65f7c34d54/d5e3e340-2a2c-48a3-93b4-08dca611771a

ADMIN_USER_IDS=59,753,19,224,682,95,62,92,9,66

V2_APPLY_DATE="2023-07-01"
ID_OPEN_PROJECT_DL_WF=2006
EXCHANGE_WORK_DAYS=2024-05-04

BACKLOG_SPACE_ID=amelainc
BACKLOG_API_KEY=
ISSUES_CREATED_SINCE="2024-01-01"

# Docker
APP_PORT=8083
